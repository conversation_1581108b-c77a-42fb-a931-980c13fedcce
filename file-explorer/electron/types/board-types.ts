// electron/types/board-types.ts
// Shared types for board operations between main and renderer processes

export interface Card {
  id: string
  title: string
  description?: string
  columnId: string
  swimlaneId?: string
  priority?: "low" | "medium" | "high"
  type?: string
  assignedTo?: string
  assignedAgentId?: string | null  // ✅ New field for agent assignment
  dueDate?: string
  tags?: string[]
  createdAt: string
  updatedAt: string
  taskHistory: TaskHistoryEntry[]
}

export interface TaskHistoryEntry {
  timestamp: string
  action: string
  agentId: string
  details: string
}

export interface Column {
  id: string
  title: string
  cards: Card[]
}

export interface Swimlane {
  id: string
  title: string
  isExpanded: boolean
}

export interface CardType {
  id: string
  name: string
  color: string
}

export interface Agent {
  id: string
  name: string
  type: string
  status: string
}

export interface BoardFull {
  id: string
  name: string
  description?: string
  columns: Column[]
  swimlanes: Swimlane[]
  cardTypes: CardType[]
  agents: Agent[]
}
