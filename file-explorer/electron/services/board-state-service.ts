// electron/services/board-state-service.ts
import { ipc<PERSON><PERSON>, BrowserWindow } from 'electron';
import { BOARD_COMMANDS, BOARD_EVENTS } from '../board-constants';
import type { BoardFull, Card, Column, Swimlane, CardType, Agent } from '../types/board-types';

// In-memory store for all board states, keyed by boardId
const boardStates = new Map<string, BoardFull>();

// Default data for new boards - consider moving to a shared location if used elsewhere
const defaultCardTypes: CardType[] = [
  { id: "low", name: "Low", color: "#22c55e" },
  { id: "medium", name: "Medium", color: "#facc15" },
  { id: "high", name: "High", color: "#ef4444" },
];

const defaultColumns: Column[] = [
  { id: "column-1", title: "Backlog", cards: [] },
  { id: "column-2", title: "Ready", cards: [] },
  { id: "column-3", title: "In Development", cards: [] },
  { id: "column-4", title: "In Review", cards: [] },
  { id: "column-5", title: "Testing / QA", cards: [] },
  { id: "column-6", title: "Done", cards: [] },
];

const defaultSwimlanes: Swimlane[] = [
  { id: "swimlane-1", title: "Default Swimlane", isExpanded: true },
];

const defaultAgents: Agent[] = [];


export class BoardStateService {
  private registeredWindows = new Set<BrowserWindow>();

  constructor() {
    this.initializeDefaultBoard();
    this.registerIPCHandlers();
  }

  private initializeDefaultBoard() {
    if (boardStates.size === 0) {
      const mainBoardId = "main";
      const defaultBoard: BoardFull = {
        id: mainBoardId,
        name: "Main Development Board",
        columns: JSON.parse(JSON.stringify(defaultColumns)),
        swimlanes: JSON.parse(JSON.stringify(defaultSwimlanes)),
        cardTypes: JSON.parse(JSON.stringify(defaultCardTypes)),
        agents: JSON.parse(JSON.stringify(defaultAgents)),
        description: "Default board for development tasks.",
      };
      boardStates.set(mainBoardId, defaultBoard);
      console.log(`BoardStateService: Initialized default board "${mainBoardId}"`);
    }
  }

  public registerWindow(window: BrowserWindow) {
    this.registeredWindows.add(window);
    window.on('closed', () => {
      this.registeredWindows.delete(window);
      console.log('BoardStateService: Window unregistered');
    });
    console.log('BoardStateService: Window registered');
  }

  private broadcastStateUpdate(boardId: string, updatedBoardState?: BoardFull) {
    const stateToSend = updatedBoardState || boardStates.get(boardId);
    if (!stateToSend) return;

    this.registeredWindows.forEach(window => {
      if (!window.isDestroyed()) {
        // Send the full state of the specific board that was updated
        window.webContents.send(BOARD_EVENTS.STATE_UPDATE, boardId, stateToSend);
      }
    });
  }

  private broadcastBoardListUpdate() {
    const allBoardMetadata = Array.from(boardStates.values()).map(b => ({id: b.id, name: b.name, description: b.description}));
    this.registeredWindows.forEach(window => {
      if (!window.isDestroyed()) {
        window.webContents.send(BOARD_EVENTS.BOARD_LIST_UPDATED, allBoardMetadata);
      }
    });
  }


  private getBoard(boardId: string): BoardFull {
    if (!boardStates.has(boardId)) {
        // If a specific board is requested and doesn't exist, we might create it or throw error.
        // For now, let's assume GET_STATE for a non-existent board might imply creation or specific handling.
        // Let's create it if it's not "main" and doesn't exist.
        if (boardId !== "main") {
             console.warn(`BoardStateService: Board "${boardId}" not found. Creating a new one.`);
             return this.handleCreateBoard(boardId, `Board ${boardId}`);
        }
        throw new Error(`Board with id "${boardId}" not found.`);
    }
    return boardStates.get(boardId)!;
  }

  private handleCreateBoard(boardId: string, name: string, description?: string): BoardFull {
    if (boardStates.has(boardId)) {
      throw new Error(`Board with id "${boardId}" already exists.`);
    }
    const newBoard: BoardFull = {
      id: boardId,
      name: name,
      description: description || "",
      columns: JSON.parse(JSON.stringify(defaultColumns)),
      swimlanes: JSON.parse(JSON.stringify(defaultSwimlanes)),
      cardTypes: JSON.parse(JSON.stringify(defaultCardTypes)),
      agents: JSON.parse(JSON.stringify(defaultAgents)),
    };
    boardStates.set(boardId, newBoard);
    this.broadcastBoardListUpdate();
    this.broadcastStateUpdate(boardId, newBoard);
    return newBoard;
  }


  private registerIPCHandlers() {
    ipcMain.handle(BOARD_COMMANDS.GET_STATE, (_, boardId: string) => {
      console.log(`IPC: ${BOARD_COMMANDS.GET_STATE} for board ${boardId}`);
      try {
        return this.getBoard(boardId);
      } catch (e: any) {
        // If main board is requested and doesn't exist (shouldn't happen with initializeDefaultBoard)
        if (boardId === "main" && !boardStates.has("main")) {
            this.initializeDefaultBoard();
            return boardStates.get("main");
        }
        console.error(`Error in GET_STATE for ${boardId}: ${e.message}`);
        return null; // Or throw, depending on desired client handling
      }
    });

    ipcMain.handle(BOARD_COMMANDS.CREATE_BOARD, (_, name: string, description?: string) => {
      const boardId = name.toLowerCase().replace(/\s+/g, "-") + `-${Date.now()}`;
      return this.handleCreateBoard(boardId, name, description);
    });

    ipcMain.handle(BOARD_COMMANDS.UPDATE_BOARD_METADATA, (_, boardId: string, name: string, description?: string) => {
      const board = this.getBoard(boardId);
      board.name = name;
      board.description = description ?? board.description;
      this.broadcastBoardListUpdate();
      this.broadcastStateUpdate(boardId, board);
      return board;
    });

    ipcMain.handle(BOARD_COMMANDS.DELETE_BOARD, (_, boardId: string) => {
      if (boardStates.size <= 1 && boardStates.has(boardId)) {
        throw new Error("Cannot delete the last board.");
      }
      const deleted = boardStates.delete(boardId);
      if (deleted) {
        this.broadcastBoardListUpdate();
      }
      return deleted;
    });

    ipcMain.handle(BOARD_COMMANDS.ADD_COLUMN, (_, boardId: string, title: string) => {
      const board = this.getBoard(boardId);
      const newColumnId = `column-${Date.now()}`;
      const newColumn: Column = { id: newColumnId, title, cards: [] };
      board.columns.push(newColumn);
      this.broadcastStateUpdate(boardId, board);
      return newColumn;
    });

    ipcMain.handle(BOARD_COMMANDS.UPDATE_COLUMN, (_, boardId: string, updatedColumn: Column) => {
      const board = this.getBoard(boardId);
      const index = board.columns.findIndex((col: Column) => col.id === updatedColumn.id);
      if (index === -1) throw new Error(`Column ${updatedColumn.id} not found.`);
      board.columns[index] = updatedColumn;
      this.broadcastStateUpdate(boardId, board);
      return updatedColumn;
    });

    ipcMain.handle(BOARD_COMMANDS.DELETE_COLUMN, (_, boardId: string, columnId: string) => {
      const board = this.getBoard(boardId);
      board.columns = board.columns.filter((col: Column) => col.id !== columnId);
      this.broadcastStateUpdate(boardId, board);
      return true;
    });

    ipcMain.handle(BOARD_COMMANDS.MOVE_COLUMN, (_, boardId: string, dragId: string, overId: string | null) => {
        const board = this.getBoard(boardId);
        const oldIndex = board.columns.findIndex((col: Column) => col.id === dragId);
        if (oldIndex === -1) throw new Error(`Dragged column ${dragId} not found.`);

        const [movedColumn] = board.columns.splice(oldIndex, 1);

        if (overId === null) {
            board.columns.push(movedColumn); // Move to end
        } else {
            const newIndex = board.columns.findIndex((col: Column) => col.id === overId);
            if (newIndex === -1) throw new Error(`Target column ${overId} not found.`);
            board.columns.splice(newIndex, 0, movedColumn);
        }
        this.broadcastStateUpdate(boardId, board);
        return board.columns;
    });


    ipcMain.handle(BOARD_COMMANDS.ADD_SWIMLANE, (_, boardId: string, title: string) => {
      const board = this.getBoard(boardId);
      const newSwimlaneId = `swimlane-${Date.now()}`;
      const newSwimlane: Swimlane = { id: newSwimlaneId, title, isExpanded: true };
      board.swimlanes.push(newSwimlane);
      this.broadcastStateUpdate(boardId, board);
      return newSwimlane;
    });

    ipcMain.handle(BOARD_COMMANDS.UPDATE_SWIMLANE, (_, boardId: string, updatedSwimlane: Swimlane) => {
      const board = this.getBoard(boardId);
      const index = board.swimlanes.findIndex((s: Swimlane) => s.id === updatedSwimlane.id);
      if (index === -1) throw new Error(`Swimlane ${updatedSwimlane.id} not found.`);
      board.swimlanes[index] = updatedSwimlane;
      this.broadcastStateUpdate(boardId, board);
      return updatedSwimlane;
    });

    ipcMain.handle(BOARD_COMMANDS.DELETE_SWIMLANE, (_, boardId: string, swimlaneId: string) => {
      const board = this.getBoard(boardId);
      board.swimlanes = board.swimlanes.filter((s: Swimlane) => s.id !== swimlaneId);
      this.broadcastStateUpdate(boardId, board);
      return true;
    });

    ipcMain.handle(BOARD_COMMANDS.TOGGLE_SWIMLANE_EXPANSION, (_, boardId: string, swimlaneId: string) => {
      const board = this.getBoard(boardId);
      const swimlane = board.swimlanes.find((s: Swimlane) => s.id === swimlaneId);
      if (!swimlane) throw new Error(`Swimlane ${swimlaneId} not found.`);
      swimlane.isExpanded = !swimlane.isExpanded;
      this.broadcastStateUpdate(boardId, board);
      return swimlane;
    });

    ipcMain.handle(BOARD_COMMANDS.CREATE_CARD, (_, boardId: string, columnId: string, cardData: Omit<Card, "id" | "createdAt" | "updatedAt">) => {
      const board = this.getBoard(boardId);
      const column = board.columns.find((col: Column) => col.id === columnId);
      if (!column) throw new Error(`Column ${columnId} not found for board ${boardId}.`);

      const now = new Date().toISOString();
      const newCard: Card = {
        ...cardData,
        id: `card-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        columnId,
        createdAt: now,
        updatedAt: now,
        taskHistory: [
            ...(cardData.taskHistory || []),
            { timestamp: now, action: "created", agentId: "ipc-service", details: "Card created via IPC"}
        ]
      };
      column.cards.push(newCard);
      this.broadcastStateUpdate(boardId, board);
      return newCard;
    });

    ipcMain.handle(BOARD_COMMANDS.UPDATE_CARD, (_, boardId: string, updatedCard: Card) => {
      const board = this.getBoard(boardId);
      let cardFound = false;
      board.columns = board.columns.map((col: Column) => {
        // If card is in this column, update it
        const cardIndex = col.cards.findIndex((c: Card) => c.id === updatedCard.id);
        if (cardIndex !== -1) {
          // If columnId changed, this means the card is moving columns.
          // The current UPDATE_CARD IPC assumes it's an in-place update or the renderer handles the move.
          // For simplicity here, we'll update it if found. If columnId changed, client needs to handle move logic or use MOVE_CARD.
          // This simple update is problematic if card moves columns without using MOVE_CARD.
          // The `BoardContext` updateCardInColumn has logic to remove from old column if colId changes.
          // Let's assume for now `updatedCard.columnId` is the current column for this handler.
          // The actual move should be handled by MOVE_CARD for clarity.
          // If this update IS a move, we should remove it from other columns.
          if (col.id === updatedCard.columnId) {
            col.cards[cardIndex] = { ...updatedCard, updatedAt: new Date().toISOString() };
            cardFound = true;
          } else { // If card was in this column but is moving to `updatedCard.columnId`
            col.cards = col.cards.filter((c: Card) => c.id !== updatedCard.id);
          }
        }
        return col;
      });

      // If card moved to a new column not previously holding it
      if (!cardFound) {
        const targetCol = board.columns.find((c: Column) => c.id === updatedCard.columnId);
        if (targetCol) {
            targetCol.cards.push({ ...updatedCard, updatedAt: new Date().toISOString() });
            cardFound = true;
        }
      }

      if (!cardFound) throw new Error(`Card ${updatedCard.id} not found or column ${updatedCard.columnId} mismatch.`);

      this.broadcastStateUpdate(boardId, board);
      return updatedCard;
    });

    ipcMain.handle(BOARD_COMMANDS.DELETE_CARD, (_, boardId: string, columnId: string, cardId: string) => {
      const board = this.getBoard(boardId);
      const column = board.columns.find((col: Column) => col.id === columnId);
      if (!column) throw new Error(`Column ${columnId} not found.`);
      column.cards = column.cards.filter((c: Card) => c.id !== cardId);
      this.broadcastStateUpdate(boardId, board);
      return true;
    });

    ipcMain.handle(BOARD_COMMANDS.MOVE_CARD, (_, boardId: string, cardId: string, sourceColumnId: string, destinationColumnId: string, destinationSwimlaneId: string) => {
      const board = this.getBoard(boardId);
      const sourceCol = board.columns.find((col: Column) => col.id === sourceColumnId);
      const destCol = board.columns.find((col: Column) => col.id === destinationColumnId);

      if (!sourceCol || !destCol) throw new Error('Source or destination column not found.');

      const cardIndex = sourceCol.cards.findIndex((c: Card) => c.id === cardId);
      if (cardIndex === -1) throw new Error(`Card ${cardId} not found in source column ${sourceColumnId}.`);

      const [cardToMove] = sourceCol.cards.splice(cardIndex, 1);
      cardToMove.columnId = destinationColumnId;
      cardToMove.swimlaneId = destinationSwimlaneId;
      cardToMove.updatedAt = new Date().toISOString();
      cardToMove.taskHistory.push({ timestamp: cardToMove.updatedAt, action: "moved", agentId: "ipc-service", details: `Moved from ${sourceColumnId} to ${destinationColumnId}` });

      destCol.cards.push(cardToMove);
      this.broadcastStateUpdate(boardId, board);
      return cardToMove;
    });

    ipcMain.handle(BOARD_COMMANDS.UPDATE_CARD_TYPES, (_, boardId: string, cardTypes: CardType[]) => {
      const board = this.getBoard(boardId);
      board.cardTypes = cardTypes;
      this.broadcastStateUpdate(boardId, board);
      return board.cardTypes;
    });

    ipcMain.handle(BOARD_COMMANDS.UPDATE_AGENTS_ON_BOARD, (_, boardId: string, agents: Agent[]) => {
      const board = this.getBoard(boardId);
      board.agents = agents;
      this.broadcastStateUpdate(boardId, board);
      return board.agents;
    });

    console.log('BoardStateService: IPC Handlers Registered');
  }

  // ✅ Auto-save method for board state
  public async saveBoardState(boardState: any): Promise<void> {
    try {
      // If boardState contains a specific board, update it
      if (boardState && boardState.id) {
        boardStates.set(boardState.id, boardState);
        this.broadcastStateUpdate(boardState.id, boardState);
        console.log(`BoardStateService: Board state saved for board ${boardState.id}`);
      } else if (boardState && typeof boardState === 'object') {
        // If it's a collection of boards, update all
        Object.entries(boardState).forEach(([boardId, board]: [string, any]) => {
          if (board && board.id) {
            boardStates.set(boardId, board);
            this.broadcastStateUpdate(boardId, board);
          }
        });
        console.log('BoardStateService: Multiple board states saved');
      }

      // Broadcast board list update
      this.broadcastBoardListUpdate();

      console.log('BoardStateService: Board state saved successfully');
    } catch (error) {
      console.error('BoardStateService: Failed to save board state:', error);
      throw error;
    }
  }
}