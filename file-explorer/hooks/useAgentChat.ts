"use client"

import { useState, useCallback, useEffect } from "react"
import { CompleteAgentManager } from "@/components/agents/agent-manager-complete"
import { useSharedAgentState } from "@/components/agents/shared-agent-state"
import { AgentContext } from "@/components/agents/agent-base"
import { getChatHistoryService } from "@/services/chat-history"
import { createTaskStatusUpdate } from "@/utils/system-message-utils"
import { LLMRequestService, type StreamChunk } from "@/components/agents/llm-request-service"
import { llmIntegration } from "@/components/agents/llm-integration-service"
import { SemanticSearchService } from "@/components/background/semantic-search"
// import { getAgentConfig } from "@/components/agents/agent-configs"
import type { AgentChatMessage } from "@/types/chat"

// Re-export for backward compatibility
export type AgentMessage = AgentChatMessage

export function useAgentChat() {
  const [messages, setMessages] = useState<AgentMessage[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [enableStreaming, setEnableStreaming] = useState(true) // Default to enabled
  const [agentManager] = useState(() => new CompleteAgentManager())
  const [llmService] = useState(() => LLMRequestService.getInstance())
  const sharedState = useSharedAgentState()
  const [chatHistory] = useState(() => getChatHistoryService())
  const [isLLMInitialized, setIsLLMInitialized] = useState(false)
  const [semanticSearch] = useState(() => new SemanticSearchService())

  // Initialize LLM integration service
  useEffect(() => {
    const initializeLLM = async () => {
      try {
        if (!llmIntegration.isInitialized()) {
          console.log('useAgentChat: Initializing LLM integration service...')
          await llmIntegration.initialize()
          console.log('useAgentChat: LLM integration service initialized successfully')
        }
        setIsLLMInitialized(true)
      } catch (error) {
        console.error('useAgentChat: Failed to initialize LLM integration service:', error)
        setIsLLMInitialized(true) // Set to true anyway to allow UI to function
      }
    }

    initializeLLM()
  }, [])

  // Load chat history on mount
  useEffect(() => {
    const loadHistory = async () => {
      try {
        const savedMessages = await chatHistory.loadChatHistory()

        if (savedMessages.length === 0) {
          // Add welcome message for new chats
          const welcomeMessage: AgentMessage = {
            id: "welcome-1",
            content: "Hello! I'm your Agent System interface. I can help coordinate tasks between our specialized agents including designers, developers, and testers. What would you like to work on?",
            role: "agent",
            timestamp: new Date(),
            status: "completed",
            agentType: "micromanager"
          }
          setMessages([welcomeMessage])
          await chatHistory.saveChatHistory([welcomeMessage])
        } else {
          setMessages(savedMessages)
        }
      } catch (error) {
        console.error('Failed to load chat history:', error)
        // Fallback to welcome message
        const welcomeMessage: AgentMessage = {
          id: "welcome-1",
          content: "Hello! I'm your Agent System interface. I can help coordinate tasks between our specialized agents including designers, developers, and testers. What would you like to work on?",
          role: "agent",
          timestamp: new Date(),
          status: "completed",
          agentType: "micromanager"
        }
        setMessages([welcomeMessage])
      } finally {
        setIsLoaded(true)
      }
    }

    loadHistory()
  }, [chatHistory])

  // Listen for agent messages for multi-agent responses
  useEffect(() => {
    const handleAgentMessage = (message: any) => {
      console.log('🔔 Agent message received:', message)

      // Check if this is a response from a delegated agent
      if (message.type === 'completion' || message.type === 'info') {
        const agentResponse: AgentMessage = {
          id: `agent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          content: message.message,
          role: "agent",
          timestamp: new Date(),
          status: "completed",
          agentType: message.agentId,
          agentId: message.agentId,
          taskId: message.taskId,
          metadata: message.metadata
        }

        // Add the agent response to messages
        setMessages(prev => [...prev, agentResponse])
      }
    }

    // Subscribe to agent manager messages
    agentManager.onMessage(handleAgentMessage)

    return () => {
      agentManager.offMessage(handleAgentMessage)
    }
  }, [agentManager])

  // ✅ Task 65: Semantic Query Detection
  const detectSemanticQuery = useCallback((input: string): boolean => {
    const semanticPatterns = [
      /^(where\s+is|find|search\s+for|locate|show\s+me)/i,
      /^(what\s+is|how\s+does|explain)/i,
      /^(list\s+all|show\s+all|find\s+all)/i,
      /(functions?\s+that|methods?\s+that|classes?\s+that)/i,
      /(components?\s+that|files?\s+that|modules?\s+that)/i,
      /^(grep|search|find)\s+/i
    ]

    return semanticPatterns.some(pattern => pattern.test(input.trim()))
  }, [])

  // ✅ Task 65: Handle Semantic Query
  const handleSemanticQuery = useCallback(async (query: string): Promise<AgentMessage> => {
    try {
      console.log('🔍 Processing semantic query:', query)

      // Perform semantic search
      const searchResults = await semanticSearch.searchCode({
        query,
        maxResults: 5,
        minSimilarity: 0.3
      })

      if (searchResults.length === 0) {
        return {
          id: `semantic-${Date.now()}`,
          content: "🔍 **Semantic Search Results**\n\nNo matching code found in the current project for your query. Try rephrasing or using different keywords.",
          role: "agent",
          timestamp: new Date(),
          status: "completed",
          agentType: "semantic_search",
          metadata: { isSemanticResult: true, query, resultCount: 0 }
        }
      }

      // Format results for chat display
      let formattedResults = `🔍 **Semantic Search Results** (${searchResults.length} matches)\n\n`

      searchResults.forEach((result, index) => {
        formattedResults += `**${index + 1}. ${result.filePath}**\n`
        formattedResults += `*Similarity: ${(result.similarity * 100).toFixed(1)}%*\n`
        if (result.metadata.startLine) {
          formattedResults += `*Lines: ${result.metadata.startLine}-${result.metadata.endLine}*\n`
        }
        formattedResults += `\`\`\`${result.language}\n${result.content.substring(0, 300)}${result.content.length > 300 ? '...' : ''}\n\`\`\`\n\n`
      })

      return {
        id: `semantic-${Date.now()}`,
        content: formattedResults,
        role: "agent",
        timestamp: new Date(),
        status: "completed",
        agentType: "semantic_search",
        metadata: {
          isSemanticResult: true,
          query,
          resultCount: searchResults.length,
          results: searchResults.map(r => ({ filePath: r.filePath, similarity: r.similarity }))
        }
      }
    } catch (error) {
      console.error('Semantic search failed:', error)
      return {
        id: `semantic-error-${Date.now()}`,
        content: `🔍 **Semantic Search Error**\n\nFailed to search the codebase: ${error instanceof Error ? error.message : 'Unknown error'}`,
        role: "agent",
        timestamp: new Date(),
        status: "error",
        agentType: "semantic_search",
        metadata: { isSemanticResult: true, query, error: error instanceof Error ? error.message : 'Unknown error' }
      }
    }
  }, [semanticSearch])

  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isProcessing) return

    const userMessage: AgentMessage = {
      id: `user-${Date.now()}`,
      content: content.trim(),
      role: "user",
      timestamp: new Date(),
      status: "sent"
    }

    const updatedMessages = [...messages, userMessage]
    setMessages(updatedMessages)

    // Save immediately after user message
    await chatHistory.saveChatHistory(updatedMessages)

    setIsProcessing(true)

    try {
      // ✅ Task 65: Check for semantic queries first
      if (detectSemanticQuery(content.trim())) {
        console.log('🔍 Detected semantic query, routing to semantic search')

        const semanticResult = await handleSemanticQuery(content.trim())
        const finalMessages = [...updatedMessages, semanticResult]

        setMessages(finalMessages)
        await chatHistory.saveChatHistory(finalMessages)

        setIsProcessing(false)
        return
      }
      // Create agent context for the task
      const context: AgentContext = {
        task: content.trim(),
        metadata: {
          source: 'agent_chat',
          requestedAt: Date.now(),
          chatMessageId: userMessage.id
        }
      }

      // Add task to shared state for tracking
      await sharedState.assignTask({
        agentId: 'micromanager',
        description: content.trim(),
        status: 'pending',
        priority: 'medium'
      })

      // Submit task to Micromanager agent
      const taskId = await agentManager.assignTask('micromanager', context, 'medium')

      // Add streaming message placeholder
      const streamingMessage: AgentMessage = {
        id: `streaming-${Date.now()}`,
        content: "",
        role: "agent",
        timestamp: new Date(),
        status: "processing",
        agentType: "micromanager",
        agentId: "micromanager",
        taskId,
        isStreaming: true,
        stream: true
      }

      setMessages(prev => [...prev, streamingMessage])
      setStreamingMessageId(streamingMessage.id)

      // Start real streaming response
      await streamAgentResponse(streamingMessage.id, content.trim())

      // Emit system event for metrics
      emitAgentChatEvent('response_received', {
        agent: 'micromanager',
        taskId
      })

    } catch (error) {
      console.error('Agent chat error:', error)

      // Add error message
      const errorMessage: AgentMessage = {
        id: `error-${Date.now()}`,
        content: `❌ Agent Micromanager failed to respond: ${error instanceof Error ? error.message : 'Unknown error'}`,
        role: "system",
        timestamp: new Date(),
        status: "error",
        agentType: "micromanager"
      }

      setMessages(prev => {
        // Remove any processing/streaming messages
        const filtered = prev.filter(msg => msg.status !== "processing" && !msg.isStreaming)
        return [...filtered, errorMessage]
      })
    } finally {
      setIsProcessing(false)
      setStreamingMessageId(null)
    }
  }, [isProcessing, agentManager, sharedState, detectSemanticQuery, handleSemanticQuery, chatHistory, messages])

  // Real streaming response function
  const streamAgentResponse = useCallback(async (messageId: string, userMessage: string) => {
    try {
      // Fallback agent config for micromanager
      const agentConfig = {
        id: 'micromanager',
        name: 'Micromanager',
        provider: 'openai' as const,
        model: 'gpt-4',
        systemPrompt: 'You are a helpful AI assistant that coordinates tasks between specialized agents.',
        temperature: 0.7,
        maxTokens: 4000
      }

      if (!agentConfig) {
        throw new Error('Micromanager agent configuration not found')
      }

      // Check if streaming is enabled and supported
      const shouldStream = enableStreaming && LLMRequestService.providerSupportsStreaming(agentConfig.provider)

      if (!shouldStream) {
        console.log('Streaming disabled or not supported, falling back to regular response')
        return await simulateStreamingResponse(messageId, userMessage)
      }

      // Prepare messages for LLM
      const llmMessages = [
        {
          role: 'system' as const,
          content: agentConfig.systemPrompt || 'You are a helpful AI assistant that coordinates tasks between specialized agents.'
        },
        {
          role: 'user' as const,
          content: userMessage
        }
      ]

      let startTime = Date.now()

      // Stream the response
      const response = await llmService.callLLMStream(
        agentConfig,
        llmMessages,
        (chunk: StreamChunk) => {
          // Update the streaming message with each chunk
          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? {
                  ...msg,
                  content: chunk.content,
                  status: chunk.isComplete ? "completed" as const : "processing" as const,
                  isStreaming: !chunk.isComplete,
                  tokensUsed: chunk.tokensUsed?.total,
                  metadata: {
                    ...msg.metadata,
                    tokensUsed: chunk.tokensUsed,
                    streamingDelta: chunk.delta,
                    finishReason: chunk.finishReason
                  }
                }
              : msg
          ))

          // If streaming is complete, save to history
          if (chunk.isComplete) {
            const executionTime = Date.now() - startTime

            setMessages(prev => {
              const finalMessages = prev.map(msg =>
                msg.id === messageId
                  ? {
                      ...msg,
                      content: chunk.content,
                      status: "completed" as const,
                      isStreaming: false,
                      tokensUsed: chunk.tokensUsed?.total,
                      metadata: {
                        ...msg.metadata,
                        tokensUsed: chunk.tokensUsed,
                        executionTime,
                        finishReason: chunk.finishReason,
                        isRealStream: true
                      }
                    }
                  : msg
              )

              // Save to history asynchronously
              chatHistory.saveChatHistory(finalMessages).catch(console.error)

              return finalMessages
            })
          }
        }
      )

      console.log('✅ Streaming completed:', response)

    } catch (error) {
      console.error('Real streaming failed, falling back to simulation:', error)

      // Fallback to simulated streaming
      await simulateStreamingResponse(messageId, userMessage)
    } finally {
      setStreamingMessageId(null)
    }
  }, [enableStreaming, llmService, chatHistory])

  // Fallback streaming simulation function
  const simulateStreamingResponse = useCallback(async (messageId: string, userMessage: string) => {
    try {
      // Wait for the actual agent response
      const response = await waitForAgentResponse(messageId)

      // Simulate streaming by breaking response into chunks
      const fullContent = response.content || "I understand your request. Let me coordinate with the appropriate agents to help you with this task."
      const words = fullContent.split(' ')
      let currentContent = ""

      // Stream words with realistic timing
      for (let i = 0; i < words.length; i++) {
        currentContent += (i > 0 ? ' ' : '') + words[i]

        setMessages(prev => prev.map(msg =>
          msg.id === messageId
            ? {
                ...msg,
                content: currentContent,
                status: "processing",
                isStreaming: true
              }
            : msg
        ))

        // Random delay between 50-200ms per word for realistic streaming
        await new Promise(resolve => setTimeout(resolve, Math.random() * 150 + 50))
      }

      // Finalize the message
      setMessages(prev => {
        const finalMessages = prev.map(msg =>
          msg.id === messageId
            ? {
                ...msg,
                content: currentContent,
                status: "completed" as const,
                isStreaming: false,
                tokensUsed: response.tokensUsed,
                metadata: {
                  tokensUsed: response.tokensUsed,
                  executionTime: response.executionTime,
                  suggestions: response.suggestions,
                  isSimulatedStream: true
                }
              }
            : msg
        )

        // Save completed response to history
        chatHistory.saveChatHistory(finalMessages).catch(console.error)

        return finalMessages
      })

    } catch (error) {
      // Handle streaming error
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? {
              ...msg,
              content: `❌ Streaming failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
              status: "error" as const,
              isStreaming: false
            }
          : msg
      ))
    } finally {
      setStreamingMessageId(null)
    }
  }, [chatHistory])

  // Helper function to wait for agent response
  const waitForAgentResponse = useCallback(async (taskId: string, maxWaitTime = 30000) => {
    const startTime = Date.now()
    const pollInterval = 1000 // Poll every second

    return new Promise((resolve, reject) => {
      const poll = () => {
        // Check if task is completed in shared state
        const task = sharedState.tasks.find(t => t.id === taskId)

        if (task?.status === 'completed') {
          // Get the agent response from messages
          const agentMessage = sharedState.messages
            .filter(m => m.agentId === 'micromanager')
            .sort((a, b) => b.timestamp - a.timestamp)[0]

          resolve({
            content: agentMessage?.message || "Task completed successfully.",
            tokensUsed: task.metadata?.tokensUsed || 0,
            executionTime: Date.now() - startTime,
            suggestions: task.metadata?.suggestions || []
          })
          return
        }

        if (task?.status === 'failed') {
          reject(new Error(task.metadata?.error || 'Task execution failed'))
          return
        }

        // Check timeout
        if (Date.now() - startTime > maxWaitTime) {
          reject(new Error('Agent response timeout'))
          return
        }

        // Continue polling
        setTimeout(poll, pollInterval)
      }

      poll()
    })
  }, [sharedState])

  // Helper function to emit system events
  const emitAgentChatEvent = useCallback((eventType: string, data: any) => {
    try {
      // Add event to shared state messages for tracking
      sharedState.addMessage({
        agentId: 'system',
        message: `Agent Chat Event: ${eventType}`,
        timestamp: Date.now(),
        type: 'info',
        metadata: data
      })

      // Log for debugging
      console.log(`🔔 Agent Chat Event: ${eventType}`, data)
    } catch (error) {
      console.warn('Failed to emit agent chat event:', error)
    }
  }, [sharedState])

  const clearMessages = useCallback(async () => {
    const welcomeMessage: AgentMessage = {
      id: "welcome-1",
      content: "Hello! I'm your Agent System interface. I can help coordinate tasks between our specialized agents including designers, developers, and testers. What would you like to work on?",
      role: "agent",
      timestamp: new Date(),
      status: "completed",
      agentType: "micromanager"
    }

    setMessages([welcomeMessage])

    // Clear history and save welcome message
    await chatHistory.clearChatHistory()
    await chatHistory.saveChatHistory([welcomeMessage])
  }, [chatHistory])

  return {
    messages,
    isProcessing,
    streamingMessageId,
    isLoaded,
    enableStreaming,
    setEnableStreaming,
    sendMessage,
    clearMessages,
    chatHistory
  }
}
