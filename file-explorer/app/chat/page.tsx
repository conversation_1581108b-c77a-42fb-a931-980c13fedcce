"use client"

// app/chat/page.tsx
// ✅ Synchronized Chat Window Page for Floating Windows

import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/toaster";
import AgentChatPanel from "@/components/chat/AgentChatPanel";
import { SharedAgentStateProvider } from "@/components/agents/shared-agent-state";
import { Button } from "@/components/ui/button";
import { X, Minimize2 } from "lucide-react";

export default function ChatWindowPage() {
  const handleClose = () => {
    // Close the floating window
    if (typeof window !== 'undefined' && window.electronAPI?.closeWindow) {
      window.electronAPI.closeWindow();
    } else {
      console.warn('Cannot close window - Electron API not available');
    }
  };

  const handleMinimize = () => {
    // Minimize the floating window
    if (typeof window !== 'undefined' && window.electronAPI?.minimizeWindow) {
      window.electronAPI.minimizeWindow();
    }
  };

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <div className="h-screen w-full bg-background text-foreground">
        <div className="h-full flex flex-col">
          {/* ✅ Enhanced Header with Window Controls */}
          <div className="flex items-center justify-between p-3 border-b border-border bg-background/95 backdrop-blur-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <h1 className="text-sm font-semibold">Agent Chat (Floating)</h1>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-muted-foreground hover:text-foreground"
                onClick={handleMinimize}
                title="Minimize window"
              >
                <Minimize2 className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-muted-foreground hover:text-foreground hover:bg-red-500/10"
                onClick={handleClose}
                title="Close window"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* ✅ Synchronized Chat Content */}
          <div className="flex-1 overflow-hidden">
            <SharedAgentStateProvider>
              <AgentChatPanel onClose={handleClose} />
            </SharedAgentStateProvider>
          </div>
        </div>
        <Toaster />
      </div>
    </ThemeProvider>
  );
}
