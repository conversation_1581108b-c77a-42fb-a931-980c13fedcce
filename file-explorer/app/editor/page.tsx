"use client"

import { useState, useEffect } from "react";
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/toaster";
import CodeEditor from "@/components/code-editor";
import { EditorStateProvider, useEditorState } from "@/components/editor/editor-state-provider";
import { EditorActionProvider } from "@/components/editor/editor-action-provider";

// Inner component that uses the editor state
function EditorContent() {
  const { activeTab, openTabs, isInitialized } = useEditorState();

  // Show loading state while waiting for initial state
  if (!isInitialized) {
    return (
      <div className="h-screen w-full bg-background text-foreground flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Synchronizing editor state...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen w-full bg-background text-foreground">
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border bg-background">
          <h1 className="text-lg font-semibold">
            {activeTab ? activeTab.name : "Code Editor"}
          </h1>
          {activeTab && (
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">
                {activeTab.path}
              </span>
              <span className="text-xs text-muted-foreground">
                {openTabs.length} tab{openTabs.length !== 1 ? 's' : ''} open
              </span>
            </div>
          )}
        </div>

        {/* Editor Content */}
        <div className="flex-1 overflow-hidden">
          <CodeEditor file={activeTab} />
        </div>
      </div>
      <Toaster />
    </div>
  );
}

export default function EditorWindowPage() {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <EditorStateProvider>
        <EditorActionProvider>
          <EditorContent />
        </EditorActionProvider>
      </EditorStateProvider>
    </ThemeProvider>
  );
}
