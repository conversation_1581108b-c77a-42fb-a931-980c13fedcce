"use client"

import dynamic from 'next/dynamic';
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/toaster";
import { BoardProvider } from "@/components/kanban/board-context";
import { SearchProvider } from "@/components/kanban/search-provider";
import { AgentBoardControllerProvider } from "@/components/kanban/agent-board-controller";

const KanbanBoard = dynamic(() => import('@/components/kanban/kanban-board'), {
  ssr: false,
});

interface KanbanWindowClientProps {
  boardId: string;
}

export function KanbanWindowClient({ boardId }: KanbanWindowClientProps) {
  if (!boardId) {
    return <div className="p-8 text-center text-red-500">Board ID not provided.</div>;
  }

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <BoardProvider>
        <AgentBoardControllerProvider>
          <SearchProvider>
            <div className="flex flex-col h-screen bg-[#f5f5f5] dark:bg-[#1e1e1e]">
              {/* No header or main layout here, just the board */}
              <KanbanBoard boardId={boardId} />
            </div>
          </SearchProvider>
        </AgentBoardControllerProvider>
      </BoardProvider>
      <Toaster />
    </ThemeProvider>
  );
}
