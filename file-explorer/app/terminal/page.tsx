"use client"

import dynamic from 'next/dynamic';
import { ThemeProvider } from "next-themes";
import React from 'react';

// Dynamically import TerminalManager to avoid SSR issues if it relies on browser APIs
const TerminalManager = dynamic(() => import('@/components/terminal-manager'), {
  ssr: false,
});

export default function TerminalWindowPage() {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <div className="flex flex-col h-screen bg-background text-foreground">
        <TerminalManager isRunning={false} />
      </div>
    </ThemeProvider>
  );
}