"use client"

import { Theme<PERSON>rovider } from "next-themes";
import { Toaster } from "@/components/ui/toaster";
import { CompleteAgentSystem } from '@/components/agents/complete-integration';
import { SharedAgentStateProvider } from '@/components/agents/shared-agent-state';
import { ClientSettingsWrapper } from '@/components/settings/client-settings-wrapper';
import { useAlertNotifications } from '@/components/budget/use-threshold-alerts';
import { AlertBanner } from '@/components/budget/alert-display';

function AgentSystemContent() {
  // ✅ Initialize alert notifications for Agent System window
  useAlertNotifications();

  return (
    <div className="h-screen w-full bg-background text-foreground">
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border bg-background">
          <h1 className="text-lg font-semibold">Agent System</h1>
        </div>

        {/* ✅ Alert Banner for cost threshold alerts */}
        <div className="px-4 py-1">
          <AlertBanner />
        </div>

        {/* Agent System Content */}
        <div className="flex-1 overflow-hidden">
          <CompleteAgentSystem />
        </div>
      </div>
      <Toaster />
    </div>
  );
}

export default function AgentSystemWindowPage() {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <ClientSettingsWrapper>
        <SharedAgentStateProvider>
          <AgentSystemContent />
        </SharedAgentStateProvider>
      </ClientSettingsWrapper>
    </ThemeProvider>
  );
}
