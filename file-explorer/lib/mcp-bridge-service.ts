// lib/mcp-bridge-service.ts
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { AgentContext, AgentResponse } from '../components/agents/types';
import { LLMMessage, LLMResponse } from '../components/agents/llm-request-service';

export interface MCPConfig {
  enabled: boolean;
  serverCommand?: string;
  serverArgs?: string[];
  timeout?: number;
  maxRetries?: number;
}

export interface MCPTaskRequest {
  task: string;
  context: AgentContext;
  agentId: string;
  messages: LLMMessage[];
  metadata?: Record<string, any>;
}

export interface MCPTaskResponse {
  success: boolean;
  content: string;
  tokensUsed?: {
    prompt: number;
    completion: number;
    total: number;
  };
  model?: string;
  finishReason?: string;
  error?: string;
  mcpMetadata?: {
    serverUsed: string;
    toolsInvoked: string[];
    responseTime: number;
  };
}

export interface MCPConnection {
  client: Client;
  transport: StdioClientTransport;
  connected: boolean;
  serverInfo?: {
    name: string;
    version: string;
    capabilities: string[];
  };
}

export class MCPBridgeService {
  private static instance: MCPBridgeService;
  private connections: Map<string, MCPConnection> = new Map();
  private defaultConfig: MCPConfig = {
    enabled: false,
    timeout: 30000,
    maxRetries: 3
  };

  private constructor() {}

  public static getInstance(): MCPBridgeService {
    if (!MCPBridgeService.instance) {
      MCPBridgeService.instance = new MCPBridgeService();
    }
    return MCPBridgeService.instance;
  }

  /**
   * ✅ Initialize MCP connection for a specific server
   */
  public async initializeConnection(
    serverId: string, 
    config: MCPConfig
  ): Promise<boolean> {
    try {
      if (!config.enabled) {
        console.log(`🔌 MCP: Server ${serverId} is disabled`);
        return false;
      }

      if (!config.serverCommand) {
        console.error(`❌ MCP: No server command specified for ${serverId}`);
        return false;
      }

      console.log(`🔌 MCP: Initializing connection to ${serverId}...`);

      // Create transport for stdio communication
      const transport = new StdioClientTransport({
        command: config.serverCommand,
        args: config.serverArgs || []
      });

      // Create MCP client
      const client = new Client(
        {
          name: 'synapse-file-explorer',
          version: '1.0.0'
        },
        {
          capabilities: {
            sampling: {},
            tools: {},
            resources: {}
          }
        }
      );

      // Connect to the server
      await client.connect(transport);

      // Get server information
      const serverInfo = await client.getServerCapabilities();

      const connection: MCPConnection = {
        client,
        transport,
        connected: true,
        serverInfo: {
          name: serverId,
          version: serverInfo.protocolVersion || '1.0.0',
          capabilities: Object.keys(serverInfo.capabilities || {})
        }
      };

      this.connections.set(serverId, connection);

      console.log(`✅ MCP: Connected to ${serverId}`, {
        capabilities: connection.serverInfo?.capabilities
      });

      return true;
    } catch (error) {
      console.error(`❌ MCP: Failed to connect to ${serverId}:`, error);
      return false;
    }
  }

  /**
   * ✅ Send task to MCP model for processing
   */
  public async sendTaskToModel(
    serverId: string,
    request: MCPTaskRequest
  ): Promise<MCPTaskResponse> {
    const startTime = Date.now();

    try {
      const connection = this.connections.get(serverId);
      if (!connection || !connection.connected) {
        throw new Error(`MCP server ${serverId} is not connected`);
      }

      console.log(`🚀 MCP: Sending task to ${serverId} for agent ${request.agentId}`);

      // Convert messages to MCP format
      const mcpMessages = this.convertMessagesToMCP(request.messages);

      // Prepare MCP sampling request
      const samplingRequest = {
        messages: mcpMessages,
        maxTokens: request.metadata?.maxTokens || 4000,
        temperature: request.metadata?.temperature || 0.7,
        systemPrompt: request.metadata?.systemPrompt,
        includeContext: 'allServers' as const
      };

      // Send sampling request to MCP server
      const response = await connection.client.sampling.createMessage(samplingRequest);

      const responseTime = Date.now() - startTime;

      // Parse response
      const mcpResponse: MCPTaskResponse = {
        success: true,
        content: response.content.text || '',
        tokensUsed: {
          prompt: response.usage?.inputTokens || 0,
          completion: response.usage?.outputTokens || 0,
          total: (response.usage?.inputTokens || 0) + (response.usage?.outputTokens || 0)
        },
        model: response.model || 'mcp-model',
        finishReason: response.stopReason || 'stop',
        mcpMetadata: {
          serverUsed: serverId,
          toolsInvoked: [], // TODO: Extract from response if available
          responseTime
        }
      };

      console.log(`✅ MCP: Task completed via ${serverId} in ${responseTime}ms`);
      return mcpResponse;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      console.error(`❌ MCP: Task failed for ${serverId}:`, error);

      return {
        success: false,
        content: '',
        error: error instanceof Error ? error.message : 'Unknown MCP error',
        mcpMetadata: {
          serverUsed: serverId,
          toolsInvoked: [],
          responseTime
        }
      };
    }
  }

  /**
   * ✅ Receive and process response from MCP
   */
  public async receiveResponse(
    serverId: string,
    responseId: string
  ): Promise<MCPTaskResponse | null> {
    try {
      const connection = this.connections.get(serverId);
      if (!connection || !connection.connected) {
        console.warn(`⚠️ MCP: Server ${serverId} not connected for response ${responseId}`);
        return null;
      }

      // In a real implementation, this would retrieve a specific response
      // For now, we'll return null as responses are handled synchronously
      console.log(`📥 MCP: Attempting to receive response ${responseId} from ${serverId}`);
      return null;

    } catch (error) {
      console.error(`❌ MCP: Failed to receive response from ${serverId}:`, error);
      return null;
    }
  }

  /**
   * ✅ Sync agent state with MCP server
   */
  public async syncAgentState(
    serverId: string,
    agentId: string,
    state: {
      currentTask?: string;
      status: 'idle' | 'working' | 'error';
      lastUpdate: number;
      metadata?: Record<string, any>;
    }
  ): Promise<boolean> {
    try {
      const connection = this.connections.get(serverId);
      if (!connection || !connection.connected) {
        console.warn(`⚠️ MCP: Server ${serverId} not connected for state sync`);
        return false;
      }

      console.log(`🔄 MCP: Syncing agent ${agentId} state with ${serverId}`);

      // In a real MCP implementation, this would use server-specific state sync
      // For now, we'll log the state sync attempt
      console.log(`📊 MCP: Agent ${agentId} state:`, state);

      return true;
    } catch (error) {
      console.error(`❌ MCP: Failed to sync agent state with ${serverId}:`, error);
      return false;
    }
  }

  /**
   * ✅ Check if MCP server is available and connected
   */
  public isServerConnected(serverId: string): boolean {
    const connection = this.connections.get(serverId);
    return connection?.connected || false;
  }

  /**
   * ✅ Get list of connected MCP servers
   */
  public getConnectedServers(): string[] {
    return Array.from(this.connections.keys()).filter(serverId => 
      this.connections.get(serverId)?.connected
    );
  }

  /**
   * ✅ Get server capabilities
   */
  public getServerCapabilities(serverId: string): string[] {
    const connection = this.connections.get(serverId);
    return connection?.serverInfo?.capabilities || [];
  }

  /**
   * ✅ Disconnect from MCP server
   */
  public async disconnectServer(serverId: string): Promise<boolean> {
    try {
      const connection = this.connections.get(serverId);
      if (!connection) {
        return true; // Already disconnected
      }

      console.log(`🔌 MCP: Disconnecting from ${serverId}...`);

      if (connection.connected) {
        await connection.client.close();
        connection.connected = false;
      }

      this.connections.delete(serverId);
      console.log(`✅ MCP: Disconnected from ${serverId}`);
      return true;

    } catch (error) {
      console.error(`❌ MCP: Failed to disconnect from ${serverId}:`, error);
      return false;
    }
  }

  /**
   * ✅ Disconnect from all MCP servers
   */
  public async disconnectAll(): Promise<void> {
    const serverIds = Array.from(this.connections.keys());
    
    for (const serverId of serverIds) {
      await this.disconnectServer(serverId);
    }

    console.log('🔌 MCP: All servers disconnected');
  }

  /**
   * ✅ Convert LLM messages to MCP format
   */
  private convertMessagesToMCP(messages: LLMMessage[]): any[] {
    return messages.map(msg => ({
      role: msg.role,
      content: {
        type: 'text',
        text: msg.content
      }
    }));
  }

  /**
   * ✅ Convert MCP response to LLM format
   */
  public convertMCPToLLMResponse(mcpResponse: MCPTaskResponse): LLMResponse {
    return {
      content: mcpResponse.content,
      tokensUsed: mcpResponse.tokensUsed || { prompt: 0, completion: 0, total: 0 },
      model: mcpResponse.model || 'mcp-model',
      provider: 'mcp' as any, // Cast to satisfy type
      finishReason: mcpResponse.finishReason || 'stop',
      responseTime: mcpResponse.mcpMetadata?.responseTime || 0
    };
  }

  /**
   * ✅ Test MCP connection
   */
  public async testConnection(serverId: string): Promise<{
    success: boolean;
    latency?: number;
    capabilities?: string[];
    error?: string;
  }> {
    const startTime = Date.now();

    try {
      const connection = this.connections.get(serverId);
      if (!connection || !connection.connected) {
        return {
          success: false,
          error: `Server ${serverId} is not connected`
        };
      }

      // Send a simple test message
      const testRequest: MCPTaskRequest = {
        task: 'Test connection',
        context: { task: 'test' } as AgentContext,
        agentId: 'test-agent',
        messages: [{
          role: 'user',
          content: 'Hello, this is a connection test.'
        }],
        metadata: { maxTokens: 100, temperature: 0.1 }
      };

      const response = await this.sendTaskToModel(serverId, testRequest);
      const latency = Date.now() - startTime;

      return {
        success: response.success,
        latency,
        capabilities: connection.serverInfo?.capabilities,
        error: response.error
      };

    } catch (error) {
      return {
        success: false,
        latency: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown test error'
      };
    }
  }
}

// Export singleton instance
export const mcpBridgeService = MCPBridgeService.getInstance();
