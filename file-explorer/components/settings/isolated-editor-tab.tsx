// components/settings/isolated-editor-tab.tsx
import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { EditorSettings } from './settings-manager';

interface IsolatedEditorTabProps {
  settings: EditorSettings;
  updateEditorSettings: (updates: Partial<EditorSettings>) => void;
}

/**
 * ✅ Isolated Editor Tab Component
 * Uses local state for immediate UI feedback
 * Follows the same pattern as the working Agents tab
 */
export const IsolatedEditorTab = React.memo<IsolatedEditorTabProps>(({
  settings,
  updateEditorSettings
}) => {
  // ✅ Local state for immediate UI feedback
  const [localSettings, setLocalSettings] = useState<EditorSettings>(settings);

  useEffect(() => {
    console.log('🔄 IsolatedEditorTab rendered');
  });

  // ✅ Sync local state when global settings change
  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  // ✅ Immediate toggle handler with local state
  const handleToggle = useCallback((key: keyof EditorSettings) => {
    console.time('editor-toggle-latency');
    const newValue = !localSettings[key];
    setLocalSettings(prev => ({
      ...prev,
      [key]: newValue
    }));
    updateEditorSettings({ [key]: newValue });
    console.timeEnd('editor-toggle-latency');
  }, [localSettings, updateEditorSettings]);

  // ✅ Individual slider handlers (matching Agents tab pattern)
  const handleFontSizeChange = useCallback((value: number) => {
    console.time('editor-slider-latency');
    setLocalSettings(prev => ({ ...prev, fontSize: value }));
    console.timeEnd('editor-slider-latency');
  }, []);

  const handleTabSizeChange = useCallback((value: number) => {
    console.time('editor-slider-latency');
    setLocalSettings(prev => ({ ...prev, tabSize: value }));
    console.timeEnd('editor-slider-latency');
  }, []);

  // ✅ Commit handlers (matching Agents tab pattern)
  const commitFontSize = useCallback(() => {
    console.time('editor-slider-commit');
    updateEditorSettings({ fontSize: localSettings.fontSize });
    console.timeEnd('editor-slider-commit');
  }, [localSettings.fontSize, updateEditorSettings]);

  const commitTabSize = useCallback(() => {
    console.time('editor-slider-commit');
    updateEditorSettings({ tabSize: localSettings.tabSize });
    console.timeEnd('editor-slider-commit');
  }, [localSettings.tabSize, updateEditorSettings]);

  // ✅ Input handler with debounced commit
  const handleInputChange = useCallback((key: keyof EditorSettings, value: string) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value
    }));

    // Debounced commit for input fields
    setTimeout(() => {
      updateEditorSettings({ [key]: value });
    }, 500);
  }, [updateEditorSettings]);

  // ✅ Memoized toggle component
  const EditorToggle = React.memo(({
    id,
    label,
    settingKey
  }: {
    id: string;
    label: string;
    settingKey: keyof EditorSettings;
  }) => (
    <div className="flex items-center justify-between">
      <Label htmlFor={id}>{label}</Label>
      <Switch
        id={id}
        checked={localSettings[settingKey] as boolean}
        onCheckedChange={() => handleToggle(settingKey)}
      />
    </div>
  ));

  // ✅ Memoized slider components (matching Agents tab pattern)
  const FontSizeSlider = React.memo(() => (
    <div className="space-y-2">
      <Label>Font Size</Label>
      <Slider
        value={[localSettings.fontSize]}
        onValueChange={([value]) => handleFontSizeChange(value)}
        onPointerUp={commitFontSize}
        min={10}
        max={24}
        step={1}
      />
      <div className="text-sm text-muted-foreground">
        {localSettings.fontSize}px
      </div>
    </div>
  ));

  const TabSizeSlider = React.memo(() => (
    <div className="space-y-2">
      <Label>Tab Size</Label>
      <Slider
        value={[localSettings.tabSize]}
        onValueChange={([value]) => handleTabSizeChange(value)}
        onPointerUp={commitTabSize}
        min={2}
        max={8}
        step={1}
      />
      <div className="text-sm text-muted-foreground">
        {localSettings.tabSize} spaces
      </div>
    </div>
  ));

  // ✅ Memoized input component
  const EditorInput = React.memo(({
    label,
    settingKey
  }: {
    label: string;
    settingKey: keyof EditorSettings;
  }) => (
    <div className="space-y-2">
      <Label>{label}</Label>
      <Input
        value={localSettings[settingKey] as string}
        onChange={(e) => handleInputChange(settingKey, e.target.value)}
      />
    </div>
  ));

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Editor Settings</CardTitle>
          <CardDescription>Configure code editor preferences</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <FontSizeSlider />

            <TabSizeSlider />
          </div>

          <EditorInput
            label="Font Family"
            settingKey="fontFamily"
          />

          <div className="grid grid-cols-2 gap-4">
            <EditorToggle
              id="word-wrap"
              label="Word Wrap"
              settingKey="wordWrap"
            />

            <EditorToggle
              id="line-numbers"
              label="Line Numbers"
              settingKey="lineNumbers"
            />

            <EditorToggle
              id="minimap"
              label="Minimap"
              settingKey="minimap"
            />

            <EditorToggle
              id="auto-format"
              label="Auto Format"
              settingKey="autoFormat"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
});

IsolatedEditorTab.displayName = 'IsolatedEditorTab';
