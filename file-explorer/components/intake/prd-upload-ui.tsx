// components/intake/prd-upload-ui.tsx
import React, { useState, useCallback, useRef } from 'react';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Progress } from '../ui/progress';
import { Textarea } from '../ui/textarea';
import { Upload, FileText, CheckCircle, AlertTriangle, XCircle, Loader2 } from 'lucide-react';
import { prdIntakeService, PRDValidationResult, PRDParseResult } from './prd-intake-service';

interface PRDUploadUIProps {
  onPRDUploaded?: (filePath: string, validation: PRDValidationResult) => void;
  onPRDParsed?: (result: PRDParseResult) => void;
  onValidationChange?: (isValid: boolean) => void;
  projectPath?: string;
  className?: string;
}

export const PRDUploadUI: React.FC<PRDUploadUIProps> = ({
  onPRDUploaded,
  onPRDParsed,
  onValidationChange,
  projectPath,
  className = ''
}) => {
  const [prdContent, setPrdContent] = useState('');
  const [validation, setValidation] = useState<PRDValidationResult | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isParsing, setIsParsing] = useState(false);
  const [uploadResult, setUploadResult] = useState<string | null>(null);
  const [parseResult, setParseResult] = useState<PRDParseResult | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // ✅ Handle file drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const textFile = files.find(file => 
      file.type === 'text/plain' || 
      file.name.endsWith('.txt') || 
      file.name.endsWith('.md')
    );

    if (textFile) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const content = event.target?.result as string;
        setPrdContent(content);
        validateContent(content);
      };
      reader.readAsText(textFile);
    } else {
      alert('Please upload a text file (.txt or .md)');
    }
  }, []);

  // ✅ Handle file input change
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const content = event.target?.result as string;
        setPrdContent(content);
        validateContent(content);
      };
      reader.readAsText(file);
    }
  }, []);

  // ✅ Validate PRD content
  const validateContent = useCallback((content: string) => {
    if (content.trim()) {
      const validationResult = prdIntakeService.validatePRDContent(content);
      setValidation(validationResult);
      onValidationChange?.(validationResult.isValid);
    } else {
      setValidation(null);
      onValidationChange?.(false);
    }
  }, [onValidationChange]);

  // ✅ Handle text area change
  const handleContentChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const content = e.target.value;
    setPrdContent(content);
    validateContent(content);
  }, [validateContent]);

  // ✅ Upload PRD
  const handleUpload = useCallback(async () => {
    if (!prdContent.trim()) {
      alert('Please enter or upload PRD content first');
      return;
    }

    if (!validation?.isValid) {
      alert('PRD validation failed. Please fix the issues before uploading.');
      return;
    }

    setIsUploading(true);
    try {
      const result = await prdIntakeService.uploadPRD(prdContent, projectPath);
      
      if (result.success && result.filePath && result.validation) {
        setUploadResult(result.filePath);
        onPRDUploaded?.(result.filePath, result.validation);
        console.log('✅ PRD uploaded successfully');
      } else {
        alert(`Upload failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('Upload failed due to an unexpected error');
    } finally {
      setIsUploading(false);
    }
  }, [prdContent, validation, projectPath, onPRDUploaded]);

  // ✅ Parse PRD with Taskmaster
  const handleParsePRD = useCallback(async () => {
    if (!uploadResult) {
      alert('Please upload PRD first');
      return;
    }

    setIsParsing(true);
    try {
      const result = await prdIntakeService.parsePRDWithTaskmaster(projectPath);
      setParseResult(result);
      onPRDParsed?.(result);

      if (result.success) {
        console.log(`✅ PRD parsed successfully: ${result.taskCount} tasks generated`);
      } else {
        console.error('❌ PRD parsing failed:', result.error);
      }
    } catch (error) {
      console.error('Parse error:', error);
      setParseResult({
        success: false,
        error: 'Parsing failed due to an unexpected error'
      });
    } finally {
      setIsParsing(false);
    }
  }, [uploadResult, projectPath, onPRDParsed]);

  // ✅ Get validation status icon
  const getValidationIcon = () => {
    if (!validation) return null;
    
    if (validation.isValid) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else if (validation.errors.length > 0) {
      return <XCircle className="h-5 w-5 text-red-500" />;
    } else {
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Project Requirements Document (PRD)
          </CardTitle>
          <CardDescription>
            Upload or paste your PRD to begin project orchestration. The PRD will be validated for completeness.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Drag and Drop Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              isDragOver 
                ? 'border-primary bg-primary/5' 
                : 'border-muted-foreground/25 hover:border-muted-foreground/50'
            }`}
            onDrop={handleDrop}
            onDragOver={(e) => {
              e.preventDefault();
              setIsDragOver(true);
            }}
            onDragLeave={() => setIsDragOver(false)}
          >
            <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm text-muted-foreground mb-2">
              Drag and drop your PRD file here, or click to select
            </p>
            <Button
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
            >
              Select File
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept=".txt,.md"
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>

          {/* Text Area */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Or paste PRD content directly:
            </label>
            <Textarea
              value={prdContent}
              onChange={handleContentChange}
              placeholder="Paste your Project Requirements Document here..."
              className="min-h-[200px] font-mono text-sm"
              disabled={isUploading}
            />
          </div>

          {/* Validation Results */}
          {validation && (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                {getValidationIcon()}
                <span className="font-medium">
                  Validation Score: {validation.score}/100
                </span>
                <Badge variant={validation.isValid ? 'default' : 'destructive'}>
                  {validation.isValid ? 'Valid' : 'Invalid'}
                </Badge>
              </div>

              {validation.score > 0 && (
                <Progress value={validation.score} className="w-full" />
              )}

              {/* Validation Errors */}
              {validation.errors.length > 0 && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Errors:</strong>
                    <ul className="list-disc list-inside mt-1">
                      {validation.errors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              {/* Validation Warnings */}
              {validation.warnings.length > 0 && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Recommendations:</strong>
                    <ul className="list-disc list-inside mt-1">
                      {validation.warnings.map((warning, index) => (
                        <li key={index}>{warning}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              {/* Section Coverage */}
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center gap-2">
                  {validation.sections.hasObjective ? 
                    <CheckCircle className="h-4 w-4 text-green-500" /> : 
                    <XCircle className="h-4 w-4 text-red-500" />
                  }
                  Objective/Goal
                </div>
                <div className="flex items-center gap-2">
                  {validation.sections.hasRequirements ? 
                    <CheckCircle className="h-4 w-4 text-green-500" /> : 
                    <XCircle className="h-4 w-4 text-red-500" />
                  }
                  Requirements
                </div>
                <div className="flex items-center gap-2">
                  {validation.sections.hasAcceptanceCriteria ? 
                    <CheckCircle className="h-4 w-4 text-green-500" /> : 
                    <XCircle className="h-4 w-4 text-gray-400" />
                  }
                  Acceptance Criteria
                </div>
                <div className="flex items-center gap-2">
                  {validation.sections.hasUserStories ? 
                    <CheckCircle className="h-4 w-4 text-green-500" /> : 
                    <XCircle className="h-4 w-4 text-gray-400" />
                  }
                  User Stories
                </div>
              </div>
            </div>
          )}

          {/* Upload Button */}
          <Button
            onClick={handleUpload}
            disabled={!validation?.isValid || isUploading || !prdContent.trim()}
            className="w-full"
          >
            {isUploading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Uploading PRD...
              </>
            ) : (
              'Save PRD to Project'
            )}
          </Button>

          {/* Upload Success */}
          {uploadResult && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                PRD saved successfully to: <code className="text-sm">{uploadResult}</code>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Parse Section */}
      {uploadResult && (
        <Card>
          <CardHeader>
            <CardTitle>Parse PRD with Taskmaster</CardTitle>
            <CardDescription>
              Generate structured tasks from your PRD using the Taskmaster CLI tool.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={handleParsePRD}
              disabled={isParsing}
              className="w-full"
            >
              {isParsing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Parsing PRD with Taskmaster...
                </>
              ) : (
                'Parse PRD with Taskmaster'
              )}
            </Button>

            {/* Parse Results */}
            {parseResult && (
              <Alert variant={parseResult.success ? 'default' : 'destructive'}>
                {parseResult.success ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <XCircle className="h-4 w-4" />
                )}
                <AlertDescription>
                  {parseResult.success ? (
                    <>
                      <strong>Success!</strong> Generated {parseResult.taskCount} tasks.
                      <br />
                      Tasks saved to: <code className="text-sm">{parseResult.tasksFilePath}</code>
                    </>
                  ) : (
                    <>
                      <strong>Parsing failed:</strong> {parseResult.error}
                      {parseResult.output && (
                        <details className="mt-2">
                          <summary className="cursor-pointer">Show output</summary>
                          <pre className="text-xs mt-1 p-2 bg-muted rounded">
                            {parseResult.output}
                          </pre>
                        </details>
                      )}
                    </>
                  )}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
