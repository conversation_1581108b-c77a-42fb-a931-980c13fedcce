"use client"

import React from "react";
import { Button } from "@/components/ui/button";
import { Terminal, LayoutGrid } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

declare global {
  interface Window {
    electronAPI?: {
      openTerminalWindow: () => void;
      openKanbanWindow: (boardId: string) => void;
    };
  }
}

interface WindowLauncherProps {
  // Optional: You might pass a default board ID or let the user select one
  defaultKanbanBoardId?: string;
}

export default function WindowLauncher({ defaultKanbanBoardId = "main" }: WindowLauncherProps) {
  const { toast } = useToast();

  const handleOpenTerminal = () => {
    if (typeof window !== 'undefined' && window.electronAPI?.openTerminalWindow) {
      window.electronAPI.openTerminalWindow();
    } else {
      toast({
        title: "Electron API Not Available",
        description: "Terminal window can only be opened in Electron desktop app.",
        variant: "destructive",
      });
    }
  };

  const handleOpenKanban = () => {
    if (typeof window !== 'undefined' && window.electronAPI?.openKanbanWindow) {
      // In a real app, defaultKanbanBoardId should come from active board state or user selection
      window.electronAPI.openKanbanWindow(defaultKanbanBoardId);
    } else {
      toast({
        title: "Electron API Not Available",
        description: "Kanban board window can only be opened in Electron desktop app.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex space-x-2 p-4 border-t border-editor-border bg-editor-sidebar-bg">
      <Button onClick={handleOpenTerminal} size="sm">
        <Terminal className="h-4 w-4 mr-2" />
        Open Terminal Window
      </Button>
      <Button onClick={handleOpenKanban} size="sm">
        <LayoutGrid className="h-4 w-4 mr-2" />
        Open Kanban Board
      </Button>
    </div>
  );
}