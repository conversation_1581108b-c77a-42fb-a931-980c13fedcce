// components/ui/project-status-bar.tsx

'use client'

import React, { useState, useEffect } from 'react'
import { Folder, FolderOpen } from 'lucide-react'

interface ProjectInfo {
  path: string;
  name: string;
  id?: string;
}

export function ProjectStatusBar() {
  const [activeProject, setActiveProject] = useState<ProjectInfo | null>(null)
  const [isElectron, setIsElectron] = useState(false)

  useEffect(() => {
    // Check if we're in Electron environment
    setIsElectron(typeof window !== 'undefined' && !!window.electronAPI)

    // Only initialize in Electron environment
    if (typeof window !== 'undefined' && window.electronAPI) {
      initializeProjectStatus()
    }
  }, [])

  const initializeProjectStatus = async () => {
    try {
      const { activeProjectService } = await import('../services/active-project-service')
      
      // Get current active project
      const currentProject = activeProjectService.getActiveProject()
      setActiveProject(currentProject)

      // Subscribe to project changes
      const unsubscribe = activeProjectService.onActiveProjectChange((project) => {
        setActiveProject(project)
      })

      // Cleanup subscription on unmount
      return () => {
        unsubscribe()
      }
    } catch (error) {
      console.warn('Failed to initialize project status:', error)
    }
  }

  // Don't render in web environment
  if (!isElectron) {
    return null
  }

  const truncatePath = (path: string, maxLength: number = 50): string => {
    if (path.length <= maxLength) return path
    
    const parts = path.split('/')
    if (parts.length <= 2) return path
    
    // Show first part and last part with ellipsis in between
    const first = parts[0]
    const last = parts[parts.length - 1]
    const middle = parts.slice(1, -1)
    
    if (first.length + last.length + 3 <= maxLength) {
      // Try to fit some middle parts
      let result = first
      let remaining = maxLength - first.length - last.length - 3 // 3 for ".../"
      
      for (let i = middle.length - 1; i >= 0; i--) {
        const part = middle[i]
        if (part.length + 1 <= remaining) { // +1 for "/"
          result = `${first}/.../${middle.slice(i).join('/')}/${last}`
          break
        }
      }
      
      if (result === first) {
        result = `${first}/.../${last}`
      }
      
      return result
    }
    
    return `${first}/.../${last}`
  }

  return (
    <div className="h-6 bg-editor-bg border-t border-editor-border flex items-center justify-between px-3 text-xs text-muted-foreground">
      <div className="flex items-center space-x-2 min-w-0 flex-1">
        {activeProject ? (
          <>
            <FolderOpen className="h-3 w-3 flex-shrink-0 text-blue-500" />
            <span 
              className="truncate cursor-pointer hover:text-foreground transition-colors"
              title={activeProject.path}
            >
              {truncatePath(activeProject.path)}
            </span>
          </>
        ) : (
          <>
            <Folder className="h-3 w-3 flex-shrink-0" />
            <span>No project open</span>
          </>
        )}
      </div>
      
      <div className="flex items-center space-x-4 text-xs">
        {activeProject && (
          <span className="text-muted-foreground">
            {activeProject.name}
          </span>
        )}
        <span className="text-muted-foreground">
          Ready
        </span>
      </div>
    </div>
  )
}

export default ProjectStatusBar
