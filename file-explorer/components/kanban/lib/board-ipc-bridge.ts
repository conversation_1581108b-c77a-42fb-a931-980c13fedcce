// components/kanban/lib/board-ipc-bridge.ts
import { BOARD_COMMANDS, BOARD_EVENTS } from '../../../electron/board-constants'; // Adjust path
import type { <PERSON>Full, Card, Column, Swimlane, CardType, Agent } from '../board-context'; // Adjust path

type EventHandlers = {
  onStateUpdate?: (boardId: string, state: BoardFull) => void;
  onBoardListUpdate?: (boards: {id: string, name: string, description?: string}[]) => void;
  // Add more specific event handlers if needed, e.g., onCardCreated, onCardMoved
};

export class BoardIPCBridge {
  private isElectron: boolean;
  private ipcRenderer: any; // To hold electron.ipcRenderer if available

  constructor() {
    this.isElectron = typeof window !== 'undefined' && (window as any).electronAPI;
    if (this.isElectron) {
      console.log("BoardIPCBridge: Running in Electron. IPC functionality enabled.");
      console.log("BoardIPCBridge: Available electronAPI methods:", Object.keys((window as any).electronAPI));
      if ((window as any).electronAPI?.ipc) {
        console.log("BoardIPCBridge: IPC methods available:", Object.keys((window as any).electronAPI.ipc));
      } else {
        console.warn("BoardIPCBridge: electronAPI.ipc not found");
      }
    } else {
      console.warn("BoardIPCBridge: Not running in Electron. IPC functionality will be disabled.");
    }
  }

  private getIpcRenderer() {
    // Use the generic IPC methods exposed by preload.js
    if (this.isElectron && (window as any).electronAPI?.ipc) {
        return (window as any).electronAPI.ipc;
    }
    return null;
  }


  registerEventListeners(handlers: EventHandlers): () => void {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) {
      console.warn("BoardIPCBridge: Cannot register event listeners - IPC not available");
      return () => {};
    }

    console.log("BoardIPCBridge: Registering event listeners for:", Object.keys(handlers));

    const stateUpdateListener = (boardId: string, state: BoardFull) => {
      console.log("BoardIPCBridge: Received STATE_UPDATE event for board:", boardId);
      if (handlers.onStateUpdate) handlers.onStateUpdate(boardId, state);
    };
    const boardListListener = (boards: {id: string, name: string, description?: string}[]) => {
      console.log("BoardIPCBridge: Received BOARD_LIST_UPDATED event:", boards);
      if (handlers.onBoardListUpdate) handlers.onBoardListUpdate(boards);
    };

    // Register listeners and store cleanup functions
    const cleanupStateUpdate = ipc.on(BOARD_EVENTS.STATE_UPDATE, stateUpdateListener);
    const cleanupBoardList = ipc.on(BOARD_EVENTS.BOARD_LIST_UPDATED, boardListListener);

    console.log("BoardIPCBridge: Event listeners registered successfully");

    // Return cleanup function that calls the individual cleanup functions
    return () => {
      console.log("BoardIPCBridge: Cleaning up event listeners");
      if (typeof cleanupStateUpdate === 'function') {
        cleanupStateUpdate();
      }
      if (typeof cleanupBoardList === 'function') {
        cleanupBoardList();
      }
    };
  }

  async getBoardState(boardId: string): Promise<BoardFull | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) {
      console.warn("IPC not available for getBoardState. isElectron:", this.isElectron, "ipc:", !!ipc);
      return null; // Or throw error, or return mock data for non-electron env
    }
    try {
      console.log(`Attempting to invoke ${BOARD_COMMANDS.GET_STATE} with boardId:`, boardId);
      const result = await ipc.invoke(BOARD_COMMANDS.GET_STATE, boardId);
      console.log(`Result from ${BOARD_COMMANDS.GET_STATE}:`, result);
      return result;
    } catch (error) {
      console.error(`Error invoking ${BOARD_COMMANDS.GET_STATE}:`, error);
      return null;
    }
  }

  async createBoard(name: string, description?: string): Promise<BoardFull | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.CREATE_BOARD, name, description);
  }

  async updateBoardMetadata(boardId: string, name: string, description?: string): Promise<BoardFull | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.UPDATE_BOARD_METADATA, boardId, name, description);
  }

  async deleteBoard(boardId: string): Promise<boolean | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.DELETE_BOARD, boardId);
  }

  async addColumn(boardId: string, title: string): Promise<Column | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.ADD_COLUMN, boardId, title);
  }

  async updateColumn(boardId: string, columnData: Column): Promise<Column | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.UPDATE_COLUMN, boardId, columnData);
  }

  async deleteColumn(boardId: string, columnId: string): Promise<boolean | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.DELETE_COLUMN, boardId, columnId);
  }

  async moveColumn(boardId: string, dragId: string, overId: string | null): Promise<Column[] | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.MOVE_COLUMN, boardId, dragId, overId);
  }

  async addSwimlane(boardId: string, title: string): Promise<Swimlane | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.ADD_SWIMLANE, boardId, title);
  }

  async updateSwimlane(boardId: string, swimlaneData: Swimlane): Promise<Swimlane | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.UPDATE_SWIMLANE, boardId, swimlaneData);
  }

  async deleteSwimlane(boardId: string, swimlaneId: string): Promise<boolean | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.DELETE_SWIMLANE, boardId, swimlaneId);
  }

  async toggleSwimlaneExpansion(boardId: string, swimlaneId: string): Promise<Swimlane | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.TOGGLE_SWIMLANE_EXPANSION, boardId, swimlaneId);
  }

  async createCard(boardId: string, columnId: string, cardData: Omit<Card, "id" | "createdAt" | "updatedAt">): Promise<Card | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) {
      console.warn("IPC not available for createCard. isElectron:", this.isElectron, "ipc:", !!ipc);
      return null;
    }
    try {
      console.log(`Attempting to invoke ${BOARD_COMMANDS.CREATE_CARD} with:`, { boardId, columnId, cardData });
      const result = await ipc.invoke(BOARD_COMMANDS.CREATE_CARD, boardId, columnId, cardData);
      console.log(`Result from ${BOARD_COMMANDS.CREATE_CARD}:`, result);
      return result;
    } catch (error) {
      console.error(`Error invoking ${BOARD_COMMANDS.CREATE_CARD}:`, error);
      return null;
    }
  }

  async updateCard(boardId: string, cardData: Card): Promise<Card | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.UPDATE_CARD, boardId, cardData);
  }

  async deleteCard(boardId: string, columnId: string, cardId: string): Promise<boolean | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.DELETE_CARD, boardId, columnId, cardId);
  }

  async moveCard(boardId: string, cardId: string, sourceColumnId: string, destinationColumnId: string, destinationSwimlaneId: string): Promise<Card | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) {
      console.warn("IPC not available for moveCard. isElectron:", this.isElectron, "ipc:", !!ipc);
      return null;
    }
    try {
      console.log(`Attempting to invoke ${BOARD_COMMANDS.MOVE_CARD} with:`, { boardId, cardId, sourceColumnId, destinationColumnId, destinationSwimlaneId });
      const result = await ipc.invoke(BOARD_COMMANDS.MOVE_CARD, boardId, cardId, sourceColumnId, destinationColumnId, destinationSwimlaneId);
      console.log(`Result from ${BOARD_COMMANDS.MOVE_CARD}:`, result);
      return result;
    } catch (error) {
      console.error(`Error invoking ${BOARD_COMMANDS.MOVE_CARD}:`, error);
      return null;
    }
  }

  async updateCardTypes(boardId: string, cardTypes: CardType[]): Promise<CardType[] | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.UPDATE_CARD_TYPES, boardId, cardTypes);
  }

  async updateAgentsOnBoard(boardId: string, agents: Agent[]): Promise<Agent[] | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.UPDATE_AGENTS_ON_BOARD, boardId, agents);
  }

  /**
   * ✅ Update card progress by updating the card's progress field
   */
  async updateCardProgress(boardId: string, cardId: string, progress: number, agentId: string): Promise<Card | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) {
      console.warn("IPC not available for updateCardProgress. isElectron:", this.isElectron, "ipc:", !!ipc);
      return null;
    }

    try {
      // First, get the current board state to find the card
      const boardState = await this.getBoardState(boardId);
      if (!boardState) {
        console.error(`BoardIPCBridge: Board ${boardId} not found for progress update`);
        return null;
      }

      // Find the card across all columns
      let targetCard: Card | null = null;
      for (const column of boardState.columns) {
        const card = column.cards.find(c => c.id === cardId);
        if (card) {
          targetCard = card;
          break;
        }
      }

      if (!targetCard) {
        console.error(`BoardIPCBridge: Card ${cardId} not found for progress update`);
        return null;
      }

      // Update the card with new progress
      const updatedCard: Card = {
        ...targetCard,
        progress: Math.max(0, Math.min(100, progress)), // Clamp between 0-100
        updatedAt: new Date().toISOString(),
        taskHistory: [
          ...(targetCard.taskHistory || []),
          {
            timestamp: new Date().toISOString(),
            action: 'progress_updated',
            agentId,
            details: `Progress updated to ${progress}%`
          }
        ]
      };

      console.log(`BoardIPCBridge: Updating card ${cardId} progress to ${progress}%`);
      const result = await ipc.invoke(BOARD_COMMANDS.UPDATE_CARD, boardId, updatedCard);
      console.log(`BoardIPCBridge: Card progress update result:`, result);
      return result;
    } catch (error) {
      console.error(`BoardIPCBridge: Error updating card progress for ${cardId}:`, error);
      return null;
    }
  }

  /**
   * ✅ Move card to a specific column (enhanced version with better error handling)
   */
  async moveCardToColumn(boardId: string, cardId: string, targetColumnId: string, agentId: string): Promise<Card | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) {
      console.warn("IPC not available for moveCardToColumn. isElectron:", this.isElectron, "ipc:", !!ipc);
      return null;
    }

    try {
      // First, get the current board state to find the card's current column
      const boardState = await this.getBoardState(boardId);
      if (!boardState) {
        console.error(`BoardIPCBridge: Board ${boardId} not found for card move`);
        return null;
      }

      // Find the card and its current column
      let sourceColumnId: string | null = null;
      let targetCard: Card | null = null;

      for (const column of boardState.columns) {
        const card = column.cards.find(c => c.id === cardId);
        if (card) {
          sourceColumnId = column.id;
          targetCard = card;
          break;
        }
      }

      if (!sourceColumnId || !targetCard) {
        console.error(`BoardIPCBridge: Card ${cardId} not found in any column for move operation`);
        return null;
      }

      // Check if target column exists
      const targetColumnExists = boardState.columns.some(col => col.id === targetColumnId);
      if (!targetColumnExists) {
        console.error(`BoardIPCBridge: Target column ${targetColumnId} not found`);
        return null;
      }

      // If card is already in the target column, no need to move
      if (sourceColumnId === targetColumnId) {
        console.log(`BoardIPCBridge: Card ${cardId} is already in column ${targetColumnId}`);
        return targetCard;
      }

      // Use the default swimlane ID
      const defaultSwimlaneId = boardState.swimlanes[0]?.id || 'swimlane-1';

      console.log(`BoardIPCBridge: Moving card ${cardId} from ${sourceColumnId} to ${targetColumnId}`);
      const result = await ipc.invoke(
        BOARD_COMMANDS.MOVE_CARD,
        boardId,
        cardId,
        sourceColumnId,
        targetColumnId,
        defaultSwimlaneId
      );
      console.log(`BoardIPCBridge: Card move result:`, result);
      return result;
    } catch (error) {
      console.error(`BoardIPCBridge: Error moving card ${cardId} to column ${targetColumnId}:`, error);
      return null;
    }
  }
}

export const boardIPCBridge = new BoardIPCBridge();