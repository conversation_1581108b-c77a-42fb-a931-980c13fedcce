// components/services/active-project-service.ts

/**
 * Active Project Service
 *
 * Manages the currently active project context for the Agent System.
 * Ensures all agent-generated file paths are resolved relative to the correct project directory.
 */

export interface ProjectInfo {
  path: string;
  name: string;
  id?: string;
}

export class ActiveProjectService {
  private currentProject: ProjectInfo | null = null;
  private listeners: Array<(project: ProjectInfo | null) => void> = [];

  /**
   * Set the active project for agent operations
   */
  setActiveProject(path: string, name?: string, id?: string): void {
    if (!path || path.trim() === '') {
      throw new Error('Project path cannot be empty');
    }

    // Normalize path (remove trailing slashes)
    const normalizedPath = path.replace(/\/+$/, '');

    this.currentProject = {
      path: normalizedPath,
      name: name || normalizedPath.split('/').pop() || 'Unknown Project',
      id
    };

    console.log(`ActiveProjectService: Set active project to ${normalizedPath}`);

    // Initialize semantic indexing for the project
    this.initializeSemanticIndexing(normalizedPath);

    // Notify listeners
    this.notifyListeners();
  }

  /**
   * Get the current active project path
   */
  getActiveProjectPath(): string | null {
    return this.currentProject?.path || null;
  }

  /**
   * Get the current active project info
   */
  getActiveProject(): ProjectInfo | null {
    return this.currentProject ? { ...this.currentProject } : null;
  }

  /**
   * Check if there is an active project
   */
  hasActiveProject(): boolean {
    return this.currentProject !== null;
  }

  /**
   * Resolve a relative path to an absolute path within the active project
   */
  resolve(relativePath: string): string {
    if (!this.currentProject) {
      throw new Error('No active project set. Cannot resolve file path.');
    }

    if (!relativePath || relativePath.trim() === '') {
      throw new Error('Relative path cannot be empty');
    }

    // Remove leading slashes from relative path
    const cleanRelativePath = relativePath.replace(/^\/+/, '');

    // Construct absolute path
    const absolutePath = `${this.currentProject.path}/${cleanRelativePath}`;

    console.log(`ActiveProjectService: Resolved '${relativePath}' to '${absolutePath}'`);
    return absolutePath;
  }

  /**
   * Validate that a path is within the active project directory
   */
  isPathInActiveProject(absolutePath: string): boolean {
    if (!this.currentProject) {
      return false;
    }

    const normalizedPath = absolutePath.replace(/\/+$/, '');
    const projectPath = this.currentProject.path;

    return normalizedPath.startsWith(projectPath + '/') || normalizedPath === projectPath;
  }

  /**
   * Clear the active project
   */
  clearActiveProject(): void {
    this.currentProject = null;
    console.log('ActiveProjectService: Cleared active project');
    this.notifyListeners();
  }

  /**
   * Subscribe to active project changes
   */
  onActiveProjectChange(listener: (project: ProjectInfo | null) => void): () => void {
    this.listeners.push(listener);

    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Get allowed paths for security policy
   */
  getAllowedPaths(): string[] {
    if (!this.currentProject) {
      throw new Error('No active project set. Cannot determine allowed paths.');
    }

    return [this.currentProject.path];
  }

  /**
   * Validate project path format
   */
  private validateProjectPath(path: string): boolean {
    // Basic validation - path should be absolute and not contain dangerous patterns
    if (!path.startsWith('/')) {
      return false;
    }

    // Reject paths with dangerous patterns
    const dangerousPatterns = ['..', '~', '$'];
    return !dangerousPatterns.some(pattern => path.includes(pattern));
  }

  /**
   * Notify all listeners of project change
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.currentProject);
      } catch (error) {
        console.error('Error in active project change listener:', error);
      }
    });
  }

  /**
   * Initialize semantic indexing for the project
   */
  private async initializeSemanticIndexing(projectPath: string): Promise<void> {
    try {
      // Only initialize in Electron environment
      if (typeof window !== 'undefined' && window.electronAPI) {
        const { workspaceSemanticIndexer } = await import('../workspace/semantic-indexer');
        await workspaceSemanticIndexer.initializeProject(projectPath);

        // Start indexing in background
        setTimeout(async () => {
          try {
            await workspaceSemanticIndexer.indexProject();
            console.log('Project semantic indexing completed');
          } catch (error) {
            console.warn('Failed to complete semantic indexing:', error);
          }
        }, 1000); // Delay to avoid blocking UI
      }
    } catch (error) {
      console.warn('Failed to initialize semantic indexing:', error);
    }
  }

  /**
   * Search project code semantically
   */
  async semanticSearch(query: string, limit?: number): Promise<any[]> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const { workspaceSemanticIndexer } = await import('../workspace/semantic-indexer');
        return await workspaceSemanticIndexer.semanticSearch(query, limit);
      }
      return [];
    } catch (error) {
      console.warn('Semantic search failed:', error);
      return [];
    }
  }

  /**
   * Get workspace summary
   */
  async getWorkspaceSummary(): Promise<any> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const { workspaceSemanticIndexer } = await import('../workspace/semantic-indexer');
        return workspaceSemanticIndexer.getProjectSummary();
      }
      return null;
    } catch (error) {
      console.warn('Failed to get workspace summary:', error);
      return null;
    }
  }

  /**
   * Get project statistics
   */
  getStats(): { hasActiveProject: boolean; projectPath: string | null; projectName: string | null } {
    return {
      hasActiveProject: this.hasActiveProject(),
      projectPath: this.getActiveProjectPath(),
      projectName: this.currentProject?.name || null
    };
  }
}

// Global singleton instance
export const activeProjectService = new ActiveProjectService();
