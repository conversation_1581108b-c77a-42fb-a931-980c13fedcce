"use client"

import { useState, useEffect, useRef } from "react"
import { ChevronDown, ChevronRight, Folder, FolderOpen, Search, Settings, Plus, MoreHorizontal, File as FileIconLucide, Edit3, Trash2, FolderPlus, Clock, Save } from "lucide-react"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useToast } from "@/components/ui/use-toast"

// Improved code file type icons with distinctive symbols - LARGER and more visible
export const CodeFileIcon = ({
  extension,
  className,
}: {
  extension: string
  className?: string
}) => {
  // Define icon colors for different file types
  const getIconColor = () => {
    const colors: Record<string, string> = {
      js: "text-yellow-400",
      ts: "text-blue-500",
      jsx: "text-cyan-400",
      tsx: "text-cyan-500",
      php: "text-indigo-400",
      py: "text-green-500",
      rb: "text-red-500",
      java: "text-amber-600",
      c: "text-blue-400",
      cpp: "text-blue-600",
      cs: "text-purple-500",
      go: "text-cyan-500",
      rs: "text-orange-600",
      html: "text-orange-500",
      css: "text-blue-400",
      scss: "text-pink-500",
      json: "text-yellow-600",
      yaml: "text-purple-400",
      yml: "text-purple-400",
      xml: "text-orange-400",
      md: "text-gray-400",
      sql: "text-blue-300",
      sh: "text-gray-400",
      bash: "text-gray-400",
      graphql: "text-pink-600",
      vue: "text-emerald-500",
      svelte: "text-red-600",
      dart: "text-cyan-600",
      kt: "text-purple-400",
      swift: "text-orange-500",
    }

    return colors[extension] || "text-muted-foreground"
  }

  // Get background color for icon
  const getIconBgColor = () => {
    const bgColors: Record<string, string> = {
      js: "bg-yellow-400/15",
      ts: "bg-blue-500/15",
      jsx: "bg-cyan-400/15",
      tsx: "bg-cyan-500/15",
      php: "bg-indigo-400/15",
      py: "bg-green-500/15",
      rb: "bg-red-500/15",
      java: "bg-amber-600/15",
      c: "bg-blue-400/15",
      cpp: "bg-blue-600/15",
      cs: "bg-purple-500/15",
      go: "bg-cyan-500/15",
      rs: "bg-orange-600/15",
      html: "bg-orange-500/15",
      css: "bg-blue-400/15",
      scss: "bg-pink-500/15",
      json: "bg-yellow-600/15",
      yaml: "bg-purple-400/15",
      yml: "bg-purple-400/15",
      xml: "bg-orange-400/15",
      md: "bg-gray-400/15",
      sql: "bg-blue-300/15",
      sh: "bg-gray-400/15",
      bash: "bg-gray-400/15",
      graphql: "bg-pink-600/15",
      vue: "bg-emerald-500/15",
      svelte: "bg-red-600/15",
      dart: "bg-cyan-600/15",
      kt: "bg-purple-400/15",
      swift: "bg-orange-500/15",
    }

    return bgColors[extension] || "bg-muted-foreground/15"
  }

  // Simple, clear file type indicators
  const getFileIcon = () => {
    switch (extension) {
      case "js":
        return <div className="font-bold">JS</div>
      case "ts":
        return <div className="font-bold">TS</div>
      case "jsx":
        return <div className="font-bold">JSX</div>
      case "tsx":
        return <div className="font-bold">TSX</div>
      case "php":
        return <div className="font-bold">PHP</div>
      case "py":
        return <div className="font-bold">PY</div>
      case "rb":
        return <div className="font-bold">RB</div>
      case "java":
        return <div className="font-bold">JV</div>
      case "c":
        return <div className="font-bold">C</div>
      case "cpp":
        return <div className="font-bold">C++</div>
      case "cs":
        return <div className="font-bold">C#</div>
      case "go":
        return <div className="font-bold">GO</div>
      case "rs":
        return <div className="font-bold">RS</div>
      case "html":
        return <div className="font-bold">{"<>"}</div>
      case "css":
        return <div className="font-bold">CSS</div>
      case "scss":
        return <div className="font-bold">SC</div>
      case "json":
        return <div className="font-bold">{"{}"}</div>
      case "yaml":
      case "yml":
        return <div className="font-bold">YML</div>
      case "xml":
        return <div className="font-bold">XML</div>
      case "md":
        return <div className="font-bold">MD</div>
      case "sql":
        return <div className="font-bold">SQL</div>
      case "sh":
      case "bash":
        return <div className="font-bold">SH</div>
      case "graphql":
        return <div className="font-bold">GQL</div>
      case "vue":
        return <div className="font-bold">VUE</div>
      case "svelte":
        return <div className="font-bold">SV</div>
      case "dart":
        return <div className="font-bold">DRT</div>
      case "kt":
        return <div className="font-bold">KT</div>
      case "swift":
        return <div className="font-bold">SWF</div>
      default:
        return <div className="font-bold">DOC</div>
    }
  }

  return (
    <div
      className={cn(
        "flex items-center justify-center text-xs w-5 h-5 rounded-sm",
        getIconColor(),
        getIconBgColor(),
        className,
      )}
    >
      {getFileIcon()}
    </div>
  )
}

// FileSystemItem type definition
export interface FileSystemItem {
  id: number | string
  name: string
  type: string
  path?: string
  content?: string
  size?: number
  modified?: Date
  expanded?: boolean
  files?: FileSystemItem[]
}

// Empty project structure
const emptyProjects: FileSystemItem[] = []

// Utility function to ensure unique IDs for file system items
const ensureUniqueIds = (items: any[], parentPath: string = '', baseId: number = Date.now()): any[] => {
  return items.map((item, index) => {
    const uniqueId = `${baseId}-${parentPath}-${item.name}-${index}`.replace(/[^a-zA-Z0-9-]/g, '-');
    const processedItem = {
      ...item,
      id: uniqueId,
    };

    // Recursively process nested files if they exist
    if (item.files && Array.isArray(item.files)) {
      processedItem.files = ensureUniqueIds(item.files, `${parentPath}/${item.name}`, baseId);
    }

    return processedItem;
  });
};

// Recursive component to render file tree
const FileTreeItem = ({
  item,
  level = 0,
  onToggle,
  onSelect,
  selectedFile,
}: {
  item: any
  level?: number
  onToggle: (id: number | string) => void
  onSelect: (file: any) => void
  selectedFile: any | null
}) => {
  const isFolder = item.type === "folder" || Array.isArray(item.files)
  const indent = level * 16
  const isSelected = selectedFile && selectedFile.id === item.id

  return (
    <>
      <div
        className={cn(
          "flex items-center py-1 px-2 text-sm rounded-md cursor-pointer group",
          "transition-colors duration-100",
          isSelected ? "bg-accent text-accent-foreground" : "hover:bg-accent/20",
        )}
        style={{ paddingLeft: `${indent + 8}px` }}
        onClick={(e) => {
          e.stopPropagation()
          if (isFolder) {
            onToggle(item.id)
          } else {
            onSelect(item)
          }
        }}
      >
        {isFolder ? (
          item.expanded ? (
            <ChevronDown className="sidebar-icon mr-1.5 text-muted-foreground flex-shrink-0" />
          ) : (
            <ChevronRight className="sidebar-icon mr-1.5 text-muted-foreground flex-shrink-0" />
          )
        ) : (
          <div className="w-5 mr-1.5 flex-shrink-0" />
        )}

        {isFolder ? (
          item.expanded ? (
            <FolderOpen className="file-icon mr-1.5 text-blue-400 flex-shrink-0" />
          ) : (
            <Folder className="file-icon mr-1.5 text-blue-400 flex-shrink-0" />
          )
        ) : (
          <CodeFileIcon extension={item.type} className="mr-1.5 flex-shrink-0" />
        )}

        <span className="truncate">{item.name}</span>

        {!isFolder && (
          <div className="ml-auto opacity-0 group-hover:opacity-100">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-5 w-5 text-muted-foreground">
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation()
                    console.log(`Rename ${item.name}`)
                  }}
                >
                  Rename
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation()
                    console.log(`Delete ${item.name}`)
                  }}
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {isFolder &&
        item.expanded &&
        item.files &&
        item.files.map((file: any) => (
          <FileTreeItem
            key={file.id}
            item={file}
            level={level + 1}
            onToggle={onToggle}
            onSelect={onSelect}
            selectedFile={selectedFile}
          />
        ))}
    </>
  )
}

export default function FileSidebar({ onFileSelect }: { onFileSelect: (file: any) => void }) {
  const [projects, setProjects] = useState(emptyProjects)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedFile, setSelectedFile] = useState<any>(null)
  const [showCreateProjectDialog, setShowCreateProjectDialog] = useState(false)
  const [newProjectName, setNewProjectName] = useState("")
  const [recentProjects, setRecentProjects] = useState<Array<{name: string, path: string, lastOpened: number}>>([])
  const [showUnsavedChangesDialog, setShowUnsavedChangesDialog] = useState(false)
  const [pendingProjectSwitch, setPendingProjectSwitch] = useState<{name: string, path: string} | null>(null)
  const { toast } = useToast()

  // Load recent projects on component mount
  useEffect(() => {
    loadRecentProjects()
  }, [])

  const loadRecentProjects = async () => {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const { settingsManager } = await import('../settings/settings-manager')
        const recentProjectsList = await settingsManager.getRecentProjects()
        setRecentProjects(recentProjectsList || [])
      }
    } catch (error) {
      console.warn('Failed to load recent projects:', error)
    }
  }

  const checkForUnsavedChanges = async (): Promise<boolean> => {
    try {
      // Check if Monaco editor has unsaved changes
      if (typeof window !== 'undefined' && window.monaco) {
        const models = window.monaco.editor.getModels()
        for (const model of models) {
          if (model.isAttachedToEditor() && model.getValue() !== model.getAlternativeVersionId()) {
            return true // Has unsaved changes
          }
        }
      }
      return false
    } catch (error) {
      console.warn('Failed to check for unsaved changes:', error)
      return false
    }
  }

  const saveUnsavedChanges = async (): Promise<boolean> => {
    try {
      if (typeof window !== 'undefined' && window.monaco && window.electronAPI) {
        const models = window.monaco.editor.getModels()
        let saveCount = 0

        for (const model of models) {
          if (model.isAttachedToEditor()) {
            const uri = model.uri.toString()
            const content = model.getValue()

            // Extract file path from URI
            const filePath = uri.replace('file://', '')

            try {
              const result = await window.electronAPI.saveFile(filePath, content)
              if (result.success) {
                saveCount++
              } else {
                console.error('Failed to save file:', filePath, result.error)
              }
            } catch (saveError) {
              console.error('Error saving file:', filePath, saveError)
            }
          }
        }

        if (saveCount > 0) {
          toast({
            title: "Files Saved",
            description: `Saved ${saveCount} file(s) before switching projects.`,
          })
        }

        return true
      }
      return true
    } catch (error) {
      console.error('Failed to save unsaved changes:', error)
      toast({
        title: "Save Error",
        description: "Failed to save some files. Please save manually.",
        variant: "destructive",
      })
      return false
    }
  }

  const switchToProject = async (projectName: string, projectPath: string) => {
    try {
      // Check for unsaved changes
      const hasUnsavedChanges = await checkForUnsavedChanges()

      if (hasUnsavedChanges) {
        // Show confirmation dialog
        setPendingProjectSwitch({ name: projectName, path: projectPath })
        setShowUnsavedChangesDialog(true)
        return
      }

      // No unsaved changes, proceed with switch
      await performProjectSwitch(projectName, projectPath)
    } catch (error) {
      console.error('Error switching projects:', error)
      toast({
        title: "Project Switch Error",
        description: "Failed to switch projects. Please try again.",
        variant: "destructive",
      })
    }
  }

  const performProjectSwitch = async (projectName: string, projectPath: string) => {
    try {
      // Load the selected project
      await loadProjectFromPath(projectPath, projectName)

      // Update recent projects list
      await updateRecentProjects(projectName, projectPath)

      toast({
        title: "Project Switched",
        description: `Switched to ${projectName}`,
      })
    } catch (error) {
      console.error('Error performing project switch:', error)
      toast({
        title: "Project Switch Error",
        description: "Failed to load the selected project.",
        variant: "destructive",
      })
    }
  }

  const updateRecentProjects = async (projectName: string, projectPath: string) => {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const { settingsManager } = await import('../settings/settings-manager')
        await settingsManager.addRecentProject(projectName, projectPath)
        await loadRecentProjects() // Refresh the list
      }
    } catch (error) {
      console.warn('Failed to update recent projects:', error)
    }
  }

  const handleUnsavedChangesConfirm = async (saveChanges: boolean) => {
    setShowUnsavedChangesDialog(false)

    if (!pendingProjectSwitch) return

    try {
      if (saveChanges) {
        const saveSuccess = await saveUnsavedChanges()
        if (!saveSuccess) {
          // User can choose to continue anyway or cancel
          return
        }
      }

      await performProjectSwitch(pendingProjectSwitch.name, pendingProjectSwitch.path)
    } finally {
      setPendingProjectSwitch(null)
    }
  }

  const toggleFolder = async (id: number | string) => {
    const updateFolderState = async (items: any[]): Promise<any[]> => {
      const updatedItems = [];

      for (const item of items) {
        if (item.id === id) {
          const newExpanded = !item.expanded;

          // If expanding a folder that has a path (from file system) and no files loaded yet
          if (newExpanded && item.path && item.type === 'folder' && (!item.files || item.files.length === 0)) {
            try {
              if (window.electronAPI) {
                const result = await window.electronAPI.readDirectory(item.path);
                if (result.success && result.items) {
                  const files = ensureUniqueIds(result.items.map((fsItem: any) => ({
                    ...fsItem,
                    files: fsItem.type === 'folder' ? [] : undefined
                  })), item.path || '', Date.now());
                  updatedItems.push({ ...item, expanded: newExpanded, files });
                } else {
                  updatedItems.push({ ...item, expanded: newExpanded });
                }
              } else {
                updatedItems.push({ ...item, expanded: newExpanded });
              }
            } catch (error) {
              console.error('Error loading folder contents:', error);
              updatedItems.push({ ...item, expanded: newExpanded });
            }
          } else {
            updatedItems.push({ ...item, expanded: newExpanded });
          }
        } else if (item.files && Array.isArray(item.files)) {
          const updatedFiles = await updateFolderState(item.files);
          updatedItems.push({
            ...item,
            files: updatedFiles,
          });
        } else {
          updatedItems.push(item);
        }
      }

      return updatedItems;
    }

    const updatedProjects = await updateFolderState(projects);
    setProjects(updatedProjects);
  }

  const handleFileSelect = async (file: any) => {
    console.log("File selected:", file);
    setSelectedFile(file);

    // Don't try to load content for folders
    if (file.type === 'folder') {
      console.log("Selected item is a folder, not loading content");
      onFileSelect(file);
      return;
    }

    // If the file already has content, use it
    if (file.content !== undefined) {
      console.log("File already has content, using cached version");
      onFileSelect(file);
      return;
    }

    // If the file has a path (from file system), load its content
    if (file.path && window.electronAPI) {
      console.log("Loading file content from disk:", file.path);
      try {
        const result = await window.electronAPI.readFile(file.path);
        console.log("File read result:", result.success ? "success" : "failed");

        if (result.success) {
          const fileWithContent = {
            ...file,
            content: result.content
          };
          console.log("File content loaded, length:", result.content.length);
          onFileSelect(fileWithContent);
        } else {
          console.error("Failed to read file:", result.error);
          // Still select the file even if we couldn't read it
          onFileSelect(file);
        }
      } catch (error) {
        console.error('Error reading file:', error);
        // Still select the file even if we couldn't read it
        onFileSelect(file);
      }
    } else {
      console.log("File has no path or electronAPI not available");
      onFileSelect(file);
    }
  }

  const createProject = async () => {
    if (!newProjectName.trim()) {
      return;
    }

    const projectName = newProjectName.trim();

    try {
      // Step 1: Open Folder Picker Dialog
      if (!window.electronAPI) {
        console.error("Electron API not available");
        alert('Project creation is only available in the desktop app.');
        return;
      }

      const selectedFolder = await window.electronAPI.selectFolder();
      if (!selectedFolder || !selectedFolder.success || !selectedFolder.path) {
        return; // Exit if user cancels
      }

      // Step 2: Create the New Project Folder
      const projectPath = `${selectedFolder.path}/${projectName}`;

      // First create the directory structure
      const directoryResult = await window.electronAPI.createFile(`${projectPath}/.project`, '');
      if (!directoryResult || !directoryResult.success) {
        alert(`Failed to create project directory: ${directoryResult?.error || 'Unknown error'}`);
        return;
      }

      // Step 3: Write Initial File to Disk (README.md)
      const readmeContent = `# ${projectName}

A new software project.

## Development

Start developing your application here.

## Structure

- Source files go in appropriate directories
- Documentation in docs/
- Tests in tests/

## Build

Add build instructions specific to your project type.
`;

      const readmeResult = await window.electronAPI.createFile(`${projectPath}/README.md`, readmeContent);
      if (!readmeResult || !readmeResult.success) {
        alert(`Failed to create README.md: ${readmeResult?.error || 'Unknown error'}`);
        return;
      }

      // Step 4: Register Project with Agent System
      try {
        // Only register in Electron environment
        if (typeof window !== 'undefined' && window.electronAPI) {
          const { settingsManager } = await import('../settings/settings-manager');
          await settingsManager.createProject(projectName, projectPath);
          console.log("Project registered with settings manager");
        }
      } catch (error) {
        console.warn("Failed to register project with settings manager:", error);
        // Continue anyway - project creation succeeded
      }

      // Step 5: Refresh File Explorer from Disk
      const updatedTree = await window.electronAPI.readDirectory(projectPath);
      if (updatedTree && updatedTree.success && updatedTree.items) {
        const baseId = Date.now();
        const newProject = {
          id: baseId,
          name: projectName,
          type: "folder" as const,
          path: projectPath,
          expanded: true,
          files: ensureUniqueIds(updatedTree.items.map((item: any) => ({
            ...item,
            files: item.type === 'folder' ? [] : undefined
          })), projectPath, baseId)
        };

        setProjects([...projects, newProject]);

        // Step 6: Set as Active Project for Agent System
        try {
          // Only set active project in Electron environment
          if (typeof window !== 'undefined' && window.electronAPI) {
            const { activeProjectService } = await import('../services/active-project-service');
            activeProjectService.setActiveProject(projectPath);
            console.log("Set active project for agent system:", projectPath);
          }
        } catch (error) {
          console.warn("Failed to set active project:", error);
        }
      }

      // Close dialog and reset form
      setNewProjectName("");
      setShowCreateProjectDialog(false);

    } catch (error) {
      console.error('Error creating project:', error);
      alert(`Failed to create project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  const handleCreateProject = () => {
    console.log("handleCreateProject called")
    setShowCreateProjectDialog(true)
  }

  const handleOpenProject = async () => {
    try {
      console.log("handleOpenProject called")
      // Check if we're in an Electron environment
      if (typeof window !== 'undefined') {
        // Check if electronAPI is available
        if (window.electronAPI) {
          console.log("Calling electronAPI.selectFolder")
          try {
            const result = await window.electronAPI.selectFolder();
            console.log("selectFolder result:", result)
            if (result && result.success && result.path && result.name) {
              await loadProjectFromPath(result.path, result.name);
            }
          } catch (err) {
            console.error("Error calling electronAPI.selectFolder:", err);
            alert('Failed to open project. Please check the console for details.');
          }
        } else {
          // Fallback for web environment - show a message
          console.log("electronAPI not found in window object")
          console.log("Window object keys:", Object.keys(window));
          alert('Open Project functionality is only available in the desktop app. The Electron API is not properly initialized.');
        }
      } else {
        // This should never happen in a browser environment
        console.log("Window is undefined")
        alert('Cannot access browser window object.');
      }
    } catch (error) {
      console.error('Error opening project:', error);
      alert('Failed to open project. Please try again.');
    }
  }

  const loadProjectFromPath = async (projectPath: string, projectName: string) => {
    try {
      console.log("Loading project from path:", projectPath, projectName);

      // Set as active project for Agent System
      try {
        // Only set active project in Electron environment
        if (typeof window !== 'undefined' && window.electronAPI) {
          const { activeProjectService } = await import('../services/active-project-service');
          activeProjectService.setActiveProject(projectPath, projectName);
          console.log("Set active project for agent system:", projectPath);

          // Register project with settings manager
          try {
            const { settingsManager } = await import('../settings/settings-manager');
            await settingsManager.createProject(projectName, projectPath);
            console.log("Registered opened project with settings manager");
          } catch (settingsError) {
            console.warn("Failed to register project with settings manager:", settingsError);
            // Continue anyway - project opening succeeded
          }
        }
      } catch (error) {
        console.warn("Failed to set active project:", error);
      }

      if (window.electronAPI) {
        console.log("Calling electronAPI.readDirectory");
        const result = await window.electronAPI.readDirectory(projectPath);
        console.log("readDirectory result:", result);

        if (result.success && result.items) {
          console.log("Successfully read directory, creating project structure");
          const baseId = Date.now();
          const newProject = {
            id: baseId,
            name: projectName,
            type: "folder" as const,
            path: projectPath,
            expanded: true,
            files: ensureUniqueIds(await Promise.all(result.items.map(async (item: any) => {
              if (item.type === 'folder') {
                return {
                  ...item,
                  files: [] // We'll load subdirectories on demand
                };
              }
              return item;
            })), projectPath, baseId)
          };

          console.log("New project structure:", newProject);

          // Check if project is already open
          const existingProject = projects.find(p => p.path === projectPath);
          if (!existingProject) {
            console.log("Adding new project to projects list");
            setProjects([...projects, newProject]);
          } else {
            console.log("Project already exists in projects list");
          }
        } else {
          console.error("Failed to read directory:", result.error);
          alert(`Failed to read directory: ${result.error || 'Unknown error'}`);
        }
      } else {
        console.error("electronAPI not available");
        alert('Failed to load project: Electron API not available');
      }
    } catch (error) {
      console.error('Error loading project:', error);
      alert(`Error loading project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Filter files based on search query
  const filterFiles = (items: any[], query: string): any[] => {
    if (!query) return items

    return items.reduce((filtered: any[], item) => {
      if (item.type === "folder" || Array.isArray(item.files)) {
        const filteredFiles = filterFiles(item.files || [], query)
        if (filteredFiles.length > 0 || item.name.toLowerCase().includes(query.toLowerCase())) {
          filtered.push({
            ...item,
            expanded: filteredFiles.length > 0 ? true : item.expanded,
            files: filteredFiles,
          })
        }
      } else if (item.name.toLowerCase().includes(query.toLowerCase())) {
        filtered.push(item)
      }
      return filtered
    }, [])
  }

  const filteredProjects = searchQuery ? filterFiles(projects, searchQuery) : projects

  return (
    <div className="h-full bg-editor-sidebar-bg text-editor-sidebar-fg flex flex-col">
      <div className="p-4 border-b border-editor-border">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium">Explorer</h2>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-muted-foreground hover:text-foreground flex items-center"
              onClick={handleOpenProject}
              title="Open existing project"
            >
              <FolderPlus className="h-3.5 w-3.5 mr-1" />
              <span className="text-xs">Open</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-muted-foreground hover:text-foreground flex items-center"
              onClick={handleCreateProject}
              title="Create new project"
            >
              <Plus className="h-3.5 w-3.5 mr-1" />
              <span className="text-xs">New</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-muted-foreground hover:text-foreground"
            >
              <Settings className="h-3.5 w-3.5" />
            </Button>
          </div>
        </div>

        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-3.5 w-3.5 text-muted-foreground" />
          <Input
            placeholder="Search files..."
            className="pl-8 h-9 bg-background border-input text-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-2">
          <div className="flex items-center justify-between py-1 px-2 mb-1">
            <span className="text-xs font-medium uppercase tracking-wider text-muted-foreground">Projects</span>
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-muted-foreground hover:text-foreground"
                onClick={handleOpenProject}
                title="Open existing project"
              >
                <FolderPlus className="sidebar-icon" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-muted-foreground hover:text-foreground"
                onClick={handleCreateProject}
                title="Create new project"
              >
                <Plus className="sidebar-icon" />
              </Button>
            </div>
          </div>

          {filteredProjects.length > 0 ? (
            filteredProjects.map((project) => (
              <div key={project.id} className="mb-2">
                <FileTreeItem
                  item={project}
                  onToggle={toggleFolder}
                  onSelect={handleFileSelect}
                  selectedFile={selectedFile}
                />
              </div>
            ))
          ) : (
            <div className="flex flex-col items-center justify-center py-8 px-4 text-center">
              <Folder className="h-12 w-12 text-muted-foreground/30 mb-4" />
              <p className="text-sm text-muted-foreground mb-2">No projects found</p>
              <div className="flex flex-col gap-2 mt-2 w-full max-w-[240px]">
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full justify-center"
                  onClick={handleOpenProject}
                >
                  <FolderPlus className="h-3.5 w-3.5 mr-1.5" />
                  Open Project
                </Button>
                <Button
                  size="sm"
                  className="w-full justify-center"
                  onClick={handleCreateProject}
                >
                  <Plus className="h-3.5 w-3.5 mr-1.5" />
                  Create Project
                </Button>
              </div>
            </div>
          )}

          {/* Recent Projects Section */}
          {recentProjects.length > 0 && (
            <div className="mt-4">
              <div className="flex items-center justify-between py-1 px-2 mb-1">
                <span className="text-xs font-medium uppercase tracking-wider text-muted-foreground">Recent</span>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 text-muted-foreground hover:text-foreground"
                      title="Recent projects"
                    >
                      <Clock className="sidebar-icon" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-64">
                    {recentProjects.slice(0, 5).map((project, index) => (
                      <DropdownMenuItem
                        key={`${project.path}-${index}`}
                        onClick={() => switchToProject(project.name, project.path)}
                        className="flex flex-col items-start p-3"
                      >
                        <div className="font-medium text-sm">{project.name}</div>
                        <div className="text-xs text-muted-foreground truncate w-full">
                          {project.path}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(project.lastOpened).toLocaleDateString()}
                        </div>
                      </DropdownMenuItem>
                    ))}
                    {recentProjects.length > 5 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-xs text-muted-foreground">
                          +{recentProjects.length - 5} more projects
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Show first 3 recent projects directly in sidebar */}
              {recentProjects.slice(0, 3).map((project, index) => (
                <div
                  key={`${project.path}-${index}`}
                  className="flex items-center py-1 px-2 text-sm rounded-md cursor-pointer hover:bg-accent/20 group"
                  onClick={() => switchToProject(project.name, project.path)}
                  title={project.path}
                >
                  <Clock className="sidebar-icon mr-1.5 text-muted-foreground flex-shrink-0" />
                  <span className="truncate text-muted-foreground group-hover:text-foreground">
                    {project.name}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>

      <div className="p-3 border-t border-editor-border flex items-center justify-between text-xs text-muted-foreground">
        <span>{projects.length} projects</span>
        <span>0 files</span>
      </div>

      {/* Create Project Dialog */}
      <Dialog open={showCreateProjectDialog} onOpenChange={setShowCreateProjectDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Project</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="project-name" className="text-right text-sm font-medium">
                Name
              </label>
              <Input
                id="project-name"
                value={newProjectName}
                onChange={(e) => setNewProjectName(e.target.value)}
                className="col-span-3"
                placeholder="Enter project name"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    createProject()
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowCreateProjectDialog(false)
                setNewProjectName("")
              }}
            >
              Cancel
            </Button>
            <Button type="button" onClick={createProject} disabled={!newProjectName.trim()}>
              Create Project
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Unsaved Changes Dialog */}
      <Dialog open={showUnsavedChangesDialog} onOpenChange={setShowUnsavedChangesDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Unsaved Changes</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              You have unsaved changes in the editor. Would you like to save them before switching projects?
            </p>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleUnsavedChangesConfirm(false)}
            >
              Don't Save
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowUnsavedChangesDialog(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={() => handleUnsavedChangesConfirm(true)}
            >
              <Save className="h-4 w-4 mr-2" />
              Save & Switch
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}