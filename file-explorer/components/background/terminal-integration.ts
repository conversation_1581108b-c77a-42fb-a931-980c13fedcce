// components/background/terminal-integration.ts
import { MessageBus, AgentMessage } from './message-bus';
import { TaskQueue, AgentTask } from './task-queue';
import { AgentRegistry, AgentRegistration } from './agent-registry';
import { getConfigStoreBrowser } from './config-store-browser';

export interface TerminalCommand {
  id: string;
  command: string;
  args: string[];
  workingDirectory: string;
  environment?: Record<string, string>;
  timeout?: number; // milliseconds
  interactive?: boolean;
  shell?: string; // bash, zsh, cmd, powershell
  agentId?: string; // Agent requesting the command
  priority: 'low' | 'normal' | 'high' | 'urgent';
  metadata: {
    description?: string;
    expectedOutput?: string;
    successCriteria?: string[];
    failureCriteria?: string[];
    tags: string[];
    category: 'build' | 'test' | 'deploy' | 'git' | 'npm' | 'file' | 'system' | 'custom';
  };
}

export interface TerminalProcess {
  id: string;
  commandId: string;
  pid?: number;
  status: 'starting' | 'running' | 'completed' | 'failed' | 'killed' | 'timeout';
  startTime: number;
  endTime?: number;
  exitCode?: number;
  signal?: string;
  stdout: string[];
  stderr: string[];
  combinedOutput: string[];
  workingDirectory: string;
  environment: Record<string, string>;
  resourceUsage: {
    cpuTime: number;
    memoryPeak: number;
    diskReads: number;
    diskWrites: number;
  };
}

export interface TerminalSession {
  id: string;
  agentId: string;
  shell: string;
  workingDirectory: string;
  environment: Record<string, string>;
  isActive: boolean;
  createdAt: number;
  lastActivity: number;
  commandHistory: string[];
  processes: Map<string, TerminalProcess>;
  variables: Record<string, string>;
}

export interface CommandResult {
  commandId: string;
  processId: string;
  success: boolean;
  exitCode: number;
  stdout: string;
  stderr: string;
  combinedOutput: string;
  duration: number;
  resourceUsage: TerminalProcess['resourceUsage'];
  metadata: {
    startTime: number;
    endTime: number;
    workingDirectory: string;
    environment: Record<string, string>;
    signal?: string;
    errorMessage?: string;
  };
}

export interface TerminalStats {
  totalCommands: number;
  runningCommands: number;
  completedCommands: number;
  failedCommands: number;
  averageExecutionTime: number;
  successRate: number;
  activeSessions: number;
  totalSessions: number;
  commandsByCategory: Record<string, number>;
  commandsByAgent: Record<string, number>;
  resourceUsage: {
    totalCpuTime: number;
    totalMemoryUsed: number;
    totalDiskIO: number;
  };
}

export interface TerminalConfig {
  maxConcurrentCommands: number;
  defaultTimeout: number; // milliseconds
  maxOutputLines: number;
  maxSessionAge: number; // milliseconds
  enableResourceMonitoring: boolean;
  enableCommandHistory: boolean;
  enableEnvironmentIsolation: boolean;
  allowedCommands: string[]; // Whitelist of allowed commands
  blockedCommands: string[]; // Blacklist of blocked commands
  defaultShell: string;
  defaultWorkingDirectory: string;
  environmentVariables: Record<string, string>;
}

export class TerminalIntegration {
  private messageBus: MessageBus;
  private taskQueue: TaskQueue;
  private agentRegistry: AgentRegistry;
  private configStore: any;

  private commands: Map<string, TerminalCommand> = new Map();
  private processes: Map<string, TerminalProcess> = new Map();
  private sessions: Map<string, TerminalSession> = new Map();
  private commandHistory: TerminalCommand[] = [];

  private config: TerminalConfig = {
    maxConcurrentCommands: 10,
    defaultTimeout: 30000, // 30 seconds
    maxOutputLines: 1000,
    maxSessionAge: 60 * 60 * 1000, // 1 hour
    enableResourceMonitoring: true,
    enableCommandHistory: true,
    enableEnvironmentIsolation: true,
    allowedCommands: ['npm', 'node', 'git', 'ls', 'cd', 'pwd', 'cat', 'echo', 'mkdir', 'rm', 'cp', 'mv'],
    blockedCommands: ['rm -rf /', 'format', 'fdisk', 'dd'],
    defaultShell: 'bash',
    defaultWorkingDirectory: process.cwd(),
    environmentVariables: {}
  };

  private stats: TerminalStats = {
    totalCommands: 0,
    runningCommands: 0,
    completedCommands: 0,
    failedCommands: 0,
    averageExecutionTime: 0,
    successRate: 0,
    activeSessions: 0,
    totalSessions: 0,
    commandsByCategory: {},
    commandsByAgent: {},
    resourceUsage: {
      totalCpuTime: 0,
      totalMemoryUsed: 0,
      totalDiskIO: 0
    }
  };

  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.messageBus = new MessageBus();
    this.taskQueue = new TaskQueue();
    this.agentRegistry = new AgentRegistry();
    this.configStore = getConfigStoreBrowser();

    this.setupMessageHandlers();
    this.startCleanupProcess();
  }

  /**
   * Initialize the terminal integration
   */
  async initialize(): Promise<void> {
    try {
      await this.loadConfiguration();
      await this.setupDefaultEnvironment();

      console.log('Terminal integration initialized');
    } catch (error) {
      console.error('Failed to initialize terminal integration:', error);
      throw error;
    }
  }

  /**
   * Execute a command through the terminal
   */
  async executeCommand(command: Omit<TerminalCommand, 'id'>): Promise<string> {
    const commandId = this.generateCommandId();

    const fullCommand: TerminalCommand = {
      ...command,
      id: commandId
    };

    // Validate command
    this.validateCommand(fullCommand);

    // Check security restrictions
    this.checkCommandSecurity(fullCommand);

    // Store command
    this.commands.set(commandId, fullCommand);
    this.commandHistory.push(fullCommand);

    // Update statistics
    this.stats.totalCommands++;
    this.stats.commandsByCategory[fullCommand.metadata.category] =
      (this.stats.commandsByCategory[fullCommand.metadata.category] || 0) + 1;

    if (fullCommand.agentId) {
      this.stats.commandsByAgent[fullCommand.agentId] =
        (this.stats.commandsByAgent[fullCommand.agentId] || 0) + 1;
    }

    // Create task for command execution
    const taskId = await this.createCommandTask(fullCommand);

    // Notify agents about command execution
    await this.notifyCommandExecution(fullCommand, taskId);

    console.log(`Command queued for execution: ${fullCommand.command} (${commandId})`);
    return commandId;
  }

  /**
   * Create a new terminal session for an agent
   */
  async createSession(agentId: string, options?: {
    shell?: string;
    workingDirectory?: string;
    environment?: Record<string, string>;
  }): Promise<string> {
    const sessionId = this.generateSessionId();

    const session: TerminalSession = {
      id: sessionId,
      agentId,
      shell: options?.shell || this.config.defaultShell,
      workingDirectory: options?.workingDirectory || this.config.defaultWorkingDirectory,
      environment: {
        ...this.config.environmentVariables,
        ...options?.environment
      },
      isActive: true,
      createdAt: Date.now(),
      lastActivity: Date.now(),
      commandHistory: [],
      processes: new Map(),
      variables: {}
    };

    this.sessions.set(sessionId, session);
    this.stats.totalSessions++;
    this.stats.activeSessions++;

    console.log(`Terminal session created for agent ${agentId}: ${sessionId}`);
    return sessionId;
  }

  /**
   * Execute command in a specific session
   */
  async executeInSession(sessionId: string, command: string, args: string[] = []): Promise<CommandResult> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    if (!session.isActive) {
      throw new Error(`Session ${sessionId} is not active`);
    }

    // Create command for session execution
    const terminalCommand: TerminalCommand = {
      id: this.generateCommandId(),
      command,
      args,
      workingDirectory: session.workingDirectory,
      environment: session.environment,
      shell: session.shell,
      agentId: session.agentId,
      priority: 'normal',
      metadata: {
        description: `Session command: ${command}`,
        tags: ['session', sessionId],
        category: 'custom'
      }
    };

    // Execute command
    const commandId = await this.executeCommand(terminalCommand);

    // Wait for completion and return result
    return await this.waitForCommandCompletion(commandId);
  }

  /**
   * Get command result
   */
  async getCommandResult(commandId: string): Promise<CommandResult | null> {
    const command = this.commands.get(commandId);
    if (!command) {
      return null;
    }

    // Find associated process
    const process = Array.from(this.processes.values())
      .find(p => p.commandId === commandId);

    if (!process) {
      return null;
    }

    if (process.status === 'running' || process.status === 'starting') {
      return null; // Still running
    }

    return this.createCommandResult(command, process);
  }

  /**
   * Kill a running command
   */
  async killCommand(commandId: string): Promise<boolean> {
    const process = Array.from(this.processes.values())
      .find(p => p.commandId === commandId);

    if (!process || process.status !== 'running') {
      return false;
    }

    // Update process status
    process.status = 'killed';
    process.endTime = Date.now();
    process.signal = 'SIGKILL';

    // Notify about command termination
    await this.notifyCommandTermination(commandId, 'killed');

    console.log(`Command killed: ${commandId}`);
    return true;
  }

  /**
   * Get terminal statistics
   */
  getStats(): TerminalStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * Get active sessions
   */
  getActiveSessions(): TerminalSession[] {
    return Array.from(this.sessions.values())
      .filter(session => session.isActive);
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): TerminalSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Close a terminal session
   */
  async closeSession(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    // Kill all running processes in the session
    for (const process of session.processes.values()) {
      if (process.status === 'running') {
        await this.killCommand(process.commandId);
      }
    }

    // Mark session as inactive
    session.isActive = false;
    this.stats.activeSessions--;

    console.log(`Terminal session closed: ${sessionId}`);
    return true;
  }

  /**
   * Get command history
   */
  getCommandHistory(limit?: number): TerminalCommand[] {
    const history = [...this.commandHistory];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Update configuration
   */
  async updateConfig(newConfig: Partial<TerminalConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfiguration();
    console.log('Terminal integration configuration updated');
  }

  /**
   * Get current configuration
   */
  getConfig(): TerminalConfig {
    return { ...this.config };
  }

  // Private implementation methods

  /**
   * Setup message handlers for terminal integration
   */
  private setupMessageHandlers(): void {
    // Handle command execution requests
    this.messageBus.subscribe('terminal-integration', 'execute-command', async (message: AgentMessage) => {
      try {
        const { command, args, workingDirectory, agentId } = message.payload;

        const commandId = await this.executeCommand({
          command,
          args: args || [],
          workingDirectory: workingDirectory || this.config.defaultWorkingDirectory,
          agentId,
          priority: 'normal',
          metadata: {
            description: `Agent command: ${command}`,
            tags: ['agent-request'],
            category: 'custom'
          }
        });

        // Send response back to requesting agent
        await this.messageBus.sendDirect(
          'terminal-integration',
          agentId,
          'command-queued',
          { commandId, originalMessageId: message.id }
        );
      } catch (error) {
        console.error('Error handling execute-command message:', error);

        // Send error response
        await this.messageBus.sendDirect(
          'terminal-integration',
          message.senderId,
          'command-error',
          { error: error.message, originalMessageId: message.id }
        );
      }
    });

    // Handle session creation requests
    this.messageBus.subscribe('terminal-integration', 'create-session', async (message: AgentMessage) => {
      try {
        const { shell, workingDirectory, environment } = message.payload;

        const sessionId = await this.createSession(message.senderId, {
          shell,
          workingDirectory,
          environment
        });

        await this.messageBus.sendDirect(
          'terminal-integration',
          message.senderId,
          'session-created',
          { sessionId, originalMessageId: message.id }
        );
      } catch (error) {
        console.error('Error handling create-session message:', error);

        await this.messageBus.sendDirect(
          'terminal-integration',
          message.senderId,
          'session-error',
          { error: error.message, originalMessageId: message.id }
        );
      }
    });

    // Handle command result requests
    this.messageBus.subscribe('terminal-integration', 'get-command-result', async (message: AgentMessage) => {
      try {
        const { commandId } = message.payload;
        const result = await this.getCommandResult(commandId);

        await this.messageBus.sendDirect(
          'terminal-integration',
          message.senderId,
          'command-result',
          { commandId, result, originalMessageId: message.id }
        );
      } catch (error) {
        console.error('Error handling get-command-result message:', error);

        await this.messageBus.sendDirect(
          'terminal-integration',
          message.senderId,
          'result-error',
          { error: error.message, originalMessageId: message.id }
        );
      }
    });
  }

  /**
   * Create a task for command execution
   */
  private async createCommandTask(command: TerminalCommand): Promise<string> {
    const task: Omit<AgentTask, 'id' | 'status' | 'currentRetries' | 'createdAt'> = {
      type: 'terminal-command',
      title: `Execute: ${command.command}`,
      description: command.metadata.description || `Execute command: ${command.command} ${command.args.join(' ')}`,
      payload: {
        commandId: command.id,
        command: command.command,
        args: command.args,
        workingDirectory: command.workingDirectory,
        environment: command.environment,
        shell: command.shell
      },
      priority: command.priority,
      requiredCapabilities: ['terminal-execution'],
      estimatedDuration: command.timeout || this.config.defaultTimeout,
      maxRetries: 1,
      metadata: {
        category: command.metadata.category,
        agentId: command.agentId,
        tags: command.metadata.tags
      }
    };

    return await this.taskQueue.addTask(task);
  }

  /**
   * Notify agents about command execution
   */
  private async notifyCommandExecution(command: TerminalCommand, taskId: string): Promise<void> {
    await this.messageBus.broadcast(
      'terminal-integration',
      'command-started',
      {
        commandId: command.id,
        taskId,
        command: command.command,
        args: command.args,
        agentId: command.agentId,
        category: command.metadata.category
      }
    );
  }

  /**
   * Notify agents about command termination
   */
  private async notifyCommandTermination(commandId: string, reason: string): Promise<void> {
    await this.messageBus.broadcast(
      'terminal-integration',
      'command-terminated',
      {
        commandId,
        reason,
        timestamp: Date.now()
      }
    );
  }

  /**
   * Wait for command completion
   */
  private async waitForCommandCompletion(commandId: string, timeout: number = 30000): Promise<CommandResult> {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const result = await this.getCommandResult(commandId);
      if (result) {
        return result;
      }

      // Wait 100ms before checking again
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    throw new Error(`Command ${commandId} did not complete within timeout`);
  }

  /**
   * Create command result from command and process
   */
  private createCommandResult(command: TerminalCommand, process: TerminalProcess): CommandResult {
    const duration = (process.endTime || Date.now()) - process.startTime;

    return {
      commandId: command.id,
      processId: process.id,
      success: process.status === 'completed' && (process.exitCode === 0 || process.exitCode === undefined),
      exitCode: process.exitCode || 0,
      stdout: process.stdout.join('\n'),
      stderr: process.stderr.join('\n'),
      combinedOutput: process.combinedOutput.join('\n'),
      duration,
      resourceUsage: process.resourceUsage,
      metadata: {
        startTime: process.startTime,
        endTime: process.endTime || Date.now(),
        workingDirectory: process.workingDirectory,
        environment: process.environment,
        signal: process.signal,
        errorMessage: process.status === 'failed' ? process.stderr.join('\n') : undefined
      }
    };
  }

  /**
   * Validate command before execution
   */
  private validateCommand(command: TerminalCommand): void {
    if (!command.command || command.command.trim().length === 0) {
      throw new Error('Command is required');
    }

    if (!command.workingDirectory || command.workingDirectory.trim().length === 0) {
      throw new Error('Working directory is required');
    }

    if (command.timeout && command.timeout <= 0) {
      throw new Error('Timeout must be positive');
    }

    if (!command.metadata.category) {
      throw new Error('Command category is required');
    }
  }

  /**
   * Check command security restrictions
   */
  private checkCommandSecurity(command: TerminalCommand): void {
    const baseCommand = command.command.toLowerCase();

    // Check blocked commands
    for (const blocked of this.config.blockedCommands) {
      if (baseCommand.includes(blocked.toLowerCase())) {
        throw new Error(`Command '${command.command}' is blocked for security reasons`);
      }
    }

    // Check allowed commands (if whitelist is enabled)
    if (this.config.allowedCommands.length > 0) {
      const isAllowed = this.config.allowedCommands.some(allowed =>
        baseCommand.startsWith(allowed.toLowerCase())
      );

      if (!isAllowed) {
        throw new Error(`Command '${command.command}' is not in the allowed commands list`);
      }
    }

    // Check for dangerous patterns
    const dangerousPatterns = [
      /rm\s+-rf\s+\//, // rm -rf /
      /format\s+/, // format command
      /fdisk\s+/, // fdisk command
      /dd\s+if=/, // dd command
      />\s*\/dev\//, // redirect to device files
      /sudo\s+/, // sudo commands
      /su\s+/, // su commands
    ];

    const fullCommand = `${command.command} ${command.args.join(' ')}`;
    for (const pattern of dangerousPatterns) {
      if (pattern.test(fullCommand.toLowerCase())) {
        throw new Error(`Command contains dangerous pattern and is blocked`);
      }
    }
  }

  /**
   * Update statistics
   */
  private updateStats(): void {
    // Count running commands
    this.stats.runningCommands = Array.from(this.processes.values())
      .filter(p => p.status === 'running' || p.status === 'starting').length;

    // Count completed and failed commands
    const completedProcesses = Array.from(this.processes.values())
      .filter(p => p.status === 'completed' || p.status === 'failed');

    this.stats.completedCommands = completedProcesses
      .filter(p => p.status === 'completed').length;

    this.stats.failedCommands = completedProcesses
      .filter(p => p.status === 'failed').length;

    // Calculate success rate
    const totalFinished = this.stats.completedCommands + this.stats.failedCommands;
    this.stats.successRate = totalFinished > 0 ? this.stats.completedCommands / totalFinished : 0;

    // Calculate average execution time
    const durations = completedProcesses
      .filter(p => p.endTime)
      .map(p => p.endTime! - p.startTime);

    this.stats.averageExecutionTime = durations.length > 0
      ? durations.reduce((sum, duration) => sum + duration, 0) / durations.length
      : 0;

    // Update active sessions count
    this.stats.activeSessions = Array.from(this.sessions.values())
      .filter(s => s.isActive).length;

    // Update resource usage
    this.stats.resourceUsage = Array.from(this.processes.values())
      .reduce((total, process) => ({
        totalCpuTime: total.totalCpuTime + process.resourceUsage.cpuTime,
        totalMemoryUsed: total.totalMemoryUsed + process.resourceUsage.memoryPeak,
        totalDiskIO: total.totalDiskIO + process.resourceUsage.diskReads + process.resourceUsage.diskWrites
      }), {
        totalCpuTime: 0,
        totalMemoryUsed: 0,
        totalDiskIO: 0
      });
  }

  /**
   * Generate unique command ID
   */
  private generateCommandId(): string {
    return `cmd-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique process ID
   */
  private generateProcessId(): string {
    return `proc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Setup default environment
   */
  private async setupDefaultEnvironment(): Promise<void> {
    try {
      // Set default environment variables
      this.config.environmentVariables = {
        ...this.config.environmentVariables,
        NODE_ENV: process.env.NODE_ENV || 'development',
        PATH: process.env.PATH || '',
        HOME: process.env.HOME || process.env.USERPROFILE || '',
        USER: process.env.USER || process.env.USERNAME || 'agent'
      };

      // Ensure default working directory exists
      if (typeof window === 'undefined') {
        const fs = require('fs');
        if (!fs.existsSync(this.config.defaultWorkingDirectory)) {
          this.config.defaultWorkingDirectory = process.cwd();
        }
      }
    } catch (error) {
      console.error('Error setting up default environment:', error);
    }
  }

  /**
   * Start cleanup process for expired sessions and processes
   */
  private startCleanupProcess(): void {
    // Clean up every 5 minutes
    this.cleanupTimer = setInterval(() => {
      this.performCleanup().catch(error => {
        console.error('Error during terminal cleanup:', error);
      });
    }, 5 * 60 * 1000);
  }

  /**
   * Stop cleanup process
   */
  private stopCleanupProcess(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * Perform periodic cleanup
   */
  private async performCleanup(): Promise<void> {
    try {
      const now = Date.now();
      const expiredSessions: string[] = [];
      const expiredProcesses: string[] = [];

      // Find expired sessions
      for (const [sessionId, session] of this.sessions) {
        const age = now - session.lastActivity;
        if (age > this.config.maxSessionAge && !session.isActive) {
          expiredSessions.push(sessionId);
        }
      }

      // Find old completed processes
      for (const [processId, process] of this.processes) {
        if (process.status === 'completed' || process.status === 'failed') {
          const age = now - (process.endTime || process.startTime);
          if (age > 24 * 60 * 60 * 1000) { // 24 hours
            expiredProcesses.push(processId);
          }
        }
      }

      // Remove expired sessions
      for (const sessionId of expiredSessions) {
        this.sessions.delete(sessionId);
      }

      // Remove expired processes
      for (const processId of expiredProcesses) {
        this.processes.delete(processId);
      }

      // Trim command history
      if (this.commandHistory.length > 1000) {
        this.commandHistory = this.commandHistory.slice(-1000);
      }

      if (expiredSessions.length > 0 || expiredProcesses.length > 0) {
        console.log(`Cleaned up ${expiredSessions.length} sessions and ${expiredProcesses.length} processes`);
      }
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }

  /**
   * Load configuration from storage
   */
  private async loadConfiguration(): Promise<void> {
    try {
      const stored = await this.configStore.getGlobalSetting('terminalIntegration.config');
      if (stored) {
        this.config = { ...this.config, ...stored };
      }
    } catch (error) {
      console.error('Failed to load terminal integration configuration:', error);
    }
  }

  /**
   * Save configuration to storage
   */
  private async saveConfiguration(): Promise<void> {
    try {
      await this.configStore.setGlobalSetting('terminalIntegration.config', this.config);
    } catch (error) {
      console.error('Failed to save terminal integration configuration:', error);
    }
  }

  /**
   * Simulate process execution (for browser environment)
   */
  private async simulateProcessExecution(command: TerminalCommand): Promise<TerminalProcess> {
    const processId = this.generateProcessId();
    const startTime = Date.now();

    const process: TerminalProcess = {
      id: processId,
      commandId: command.id,
      status: 'starting',
      startTime,
      stdout: [],
      stderr: [],
      combinedOutput: [],
      workingDirectory: command.workingDirectory,
      environment: command.environment || {},
      resourceUsage: {
        cpuTime: 0,
        memoryPeak: 0,
        diskReads: 0,
        diskWrites: 0
      }
    };

    this.processes.set(processId, process);

    // Simulate command execution
    setTimeout(async () => {
      try {
        process.status = 'running';

        // Simulate different command behaviors
        const result = await this.simulateCommandBehavior(command);

        process.status = 'completed';
        process.endTime = Date.now();
        process.exitCode = result.exitCode;
        process.stdout = result.stdout;
        process.stderr = result.stderr;
        process.combinedOutput = [...result.stdout, ...result.stderr];

        // Simulate resource usage
        process.resourceUsage = {
          cpuTime: Math.random() * 1000,
          memoryPeak: Math.random() * 100 * 1024 * 1024, // Random MB
          diskReads: Math.random() * 1000,
          diskWrites: Math.random() * 500
        };

        // Notify completion
        await this.notifyCommandCompletion(command.id, process);

      } catch (error) {
        process.status = 'failed';
        process.endTime = Date.now();
        process.exitCode = 1;
        process.stderr = [error.message];
        process.combinedOutput = [error.message];

        await this.notifyCommandFailure(command.id, process, error.message);
      }
    }, Math.random() * 2000 + 500); // Random delay 500-2500ms

    return process;
  }

  /**
   * Simulate command behavior based on command type
   */
  private async simulateCommandBehavior(command: TerminalCommand): Promise<{
    exitCode: number;
    stdout: string[];
    stderr: string[];
  }> {
    const cmd = command.command.toLowerCase();
    const args = command.args;

    // Simulate different commands
    if (cmd === 'echo') {
      return {
        exitCode: 0,
        stdout: [args.join(' ')],
        stderr: []
      };
    }

    if (cmd === 'pwd') {
      return {
        exitCode: 0,
        stdout: [command.workingDirectory],
        stderr: []
      };
    }

    if (cmd === 'ls' || cmd === 'dir') {
      return {
        exitCode: 0,
        stdout: ['file1.txt', 'file2.js', 'directory1/', 'directory2/'],
        stderr: []
      };
    }

    if (cmd === 'npm') {
      if (args[0] === 'install') {
        return {
          exitCode: 0,
          stdout: ['npm install completed successfully', 'Dependencies installed'],
          stderr: []
        };
      }
      if (args[0] === 'test') {
        return {
          exitCode: 0,
          stdout: ['Running tests...', 'All tests passed'],
          stderr: []
        };
      }
    }

    if (cmd === 'git') {
      if (args[0] === 'status') {
        return {
          exitCode: 0,
          stdout: ['On branch main', 'Your branch is up to date', 'nothing to commit, working tree clean'],
          stderr: []
        };
      }
      if (args[0] === 'log') {
        return {
          exitCode: 0,
          stdout: ['commit abc123 (HEAD -> main)', 'Author: Agent System', 'Date: ' + new Date().toISOString(), '', '    Latest changes'],
          stderr: []
        };
      }
    }

    // Default simulation
    return {
      exitCode: 0,
      stdout: [`Command '${command.command}' executed successfully`, `Working directory: ${command.workingDirectory}`],
      stderr: []
    };
  }

  /**
   * Notify command completion
   */
  private async notifyCommandCompletion(commandId: string, process: TerminalProcess): Promise<void> {
    await this.messageBus.broadcast(
      'terminal-integration',
      'command-completed',
      {
        commandId,
        processId: process.id,
        exitCode: process.exitCode,
        duration: (process.endTime || Date.now()) - process.startTime,
        success: process.exitCode === 0
      }
    );
  }

  /**
   * Notify command failure
   */
  private async notifyCommandFailure(commandId: string, process: TerminalProcess, error: string): Promise<void> {
    await this.messageBus.broadcast(
      'terminal-integration',
      'command-failed',
      {
        commandId,
        processId: process.id,
        error,
        exitCode: process.exitCode,
        duration: (process.endTime || Date.now()) - process.startTime
      }
    );
  }

  /**
   * Shutdown the terminal integration
   */
  async shutdown(): Promise<void> {
    try {
      this.stopCleanupProcess();

      // Kill all running processes
      for (const process of this.processes.values()) {
        if (process.status === 'running') {
          await this.killCommand(process.commandId);
        }
      }

      // Close all active sessions
      for (const session of this.sessions.values()) {
        if (session.isActive) {
          await this.closeSession(session.id);
        }
      }

      // Clear all data
      this.commands.clear();
      this.processes.clear();
      this.sessions.clear();
      this.commandHistory = [];

      console.log('Terminal integration shut down');
    } catch (error) {
      console.error('Error during terminal integration shutdown:', error);
    }
  }
}

// Global instance
let globalTerminalIntegration: TerminalIntegration | null = null;

/**
 * Get the global terminal integration instance
 */
export function getTerminalIntegration(): TerminalIntegration {
  if (!globalTerminalIntegration) {
    globalTerminalIntegration = new TerminalIntegration();
  }
  return globalTerminalIntegration;
}

// ✅ Export alias for compatibility with agent execution service
export const TerminalIntegrationManager = TerminalIntegration;
