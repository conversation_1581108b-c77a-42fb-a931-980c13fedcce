// components/agents/agent-task-coordinator.ts
import { AgentSubtask, Agent<PERSON><PERSON> } from './task-orchestrator';
import { AgentResponse } from './agent-base';
import { CompleteAgentManager } from './agent-manager-complete';
import { TaskDispatcher, TaskDispatchResult } from './task-dispatcher';
import { KanbanTaskBridge } from './kanban-task-bridge';

export interface TaskCoordinationResult {
  taskId: string;
  status: 'waiting' | 'ready' | 'running' | 'completed' | 'failed' | 'retrying';
  dependencies: string[];
  dependencyStatus: Record<string, 'pending' | 'completed' | 'failed'>;
  retryCount: number;
  maxRetries: number;
  lastError?: string;
  startTime?: number;
  completionTime?: number;
  agentId: string;
}

export interface CoordinationCallbacks {
  onTaskReady?: (taskId: string, agentId: string) => void;
  onTaskStart?: (taskId: string, agentId: string) => void;
  onTaskProgress?: (taskId: string, agentId: string, progress: number) => void;
  onTaskComplete?: (taskId: string, agentId: string, result: AgentResponse) => void;
  onTaskError?: (taskId: string, agentId: string, error: string, willRetry: boolean) => void;
  onTaskFailed?: (taskId: string, agentId: string, finalError: string) => void;
  onDependencyResolved?: (taskId: string, dependencyId: string) => void;
}

export class AgentTaskCoordinator {
  private static instance: AgentTaskCoordinator;
  private agentManager: CompleteAgentManager;
  private taskDispatcher: TaskDispatcher;
  private taskRegistry: Map<string, TaskCoordinationResult> = new Map();
  private dependencyGraph: Map<string, Set<string>> = new Map(); // taskId -> dependent tasks
  private callbacks: CoordinationCallbacks = {};
  private coordinationInterval: NodeJS.Timeout | null = null;

  constructor(agentManager: CompleteAgentManager) {
    this.agentManager = agentManager;
    this.taskDispatcher = TaskDispatcher.getInstance(agentManager);
    this.startCoordination();
  }

  public static getInstance(agentManager: CompleteAgentManager): AgentTaskCoordinator {
    if (!AgentTaskCoordinator.instance) {
      AgentTaskCoordinator.instance = new AgentTaskCoordinator(agentManager);
    }
    return AgentTaskCoordinator.instance;
  }

  /**
   * Set callbacks for coordination events
   */
  public setCallbacks(callbacks: CoordinationCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Register tasks with dependency coordination
   */
  public async registerTasks(subtasks: AgentSubtask[]): Promise<void> {
    console.log(`AgentTaskCoordinator: Registering ${subtasks.length} tasks for coordination`);

    // Clear previous state
    this.taskRegistry.clear();
    this.dependencyGraph.clear();

    // Register all tasks
    for (const subtask of subtasks) {
      const coordinationResult: TaskCoordinationResult = {
        taskId: subtask.id,
        status: subtask.dependencies && subtask.dependencies.length > 0 ? 'waiting' : 'ready',
        dependencies: subtask.dependencies || [],
        dependencyStatus: {},
        retryCount: 0,
        maxRetries: 2, // Max 2 retries as specified
        agentId: subtask.agent
      };

      // Initialize dependency status
      if (subtask.dependencies) {
        for (const depId of subtask.dependencies) {
          coordinationResult.dependencyStatus[depId] = 'pending';
        }
      }

      this.taskRegistry.set(subtask.id, coordinationResult);

      // Build dependency graph (reverse mapping)
      if (subtask.dependencies) {
        for (const depId of subtask.dependencies) {
          if (!this.dependencyGraph.has(depId)) {
            this.dependencyGraph.set(depId, new Set());
          }
          this.dependencyGraph.get(depId)!.add(subtask.id);
        }
      }
    }

    console.log(`AgentTaskCoordinator: Registered ${this.taskRegistry.size} tasks with dependency coordination`);
  }

  /**
   * Start coordinated execution of registered tasks
   */
  public async executeCoordinatedTasks(subtasks: AgentSubtask[]): Promise<void> {
    console.log(`AgentTaskCoordinator: Starting coordinated execution of ${subtasks.length} tasks`);

    // Set up task dispatcher callbacks
    this.taskDispatcher.setCallbacks({
      onTaskStart: (taskId, agentId) => {
        this.handleTaskStart(taskId, agentId);
      },
      onTaskComplete: (taskId, agentId, result) => {
        this.handleTaskComplete(taskId, agentId, result);
      },
      onTaskError: (taskId, agentId, error) => {
        this.handleTaskError(taskId, agentId, error);
      }
    });

    // Start with tasks that have no dependencies
    await this.processReadyTasks(subtasks);
  }

  /**
   * Process tasks that are ready to execute
   */
  private async processReadyTasks(allSubtasks: AgentSubtask[]): Promise<void> {
    const readyTasks = Array.from(this.taskRegistry.values())
      .filter(task => task.status === 'ready')
      .map(task => allSubtasks.find(st => st.id === task.taskId))
      .filter(Boolean) as AgentSubtask[];

    if (readyTasks.length === 0) {
      console.log('AgentTaskCoordinator: No ready tasks to process');
      return;
    }

    console.log(`AgentTaskCoordinator: Processing ${readyTasks.length} ready tasks`);

    // Dispatch ready tasks
    for (const subtask of readyTasks) {
      const taskResult = this.taskRegistry.get(subtask.id);
      if (taskResult) {
        taskResult.status = 'running';
        taskResult.startTime = Date.now();

        // Notify task ready callback
        if (this.callbacks.onTaskReady) {
          this.callbacks.onTaskReady(subtask.id, subtask.agent);
        }

        try {
          await this.taskDispatcher.dispatch(subtask);
        } catch (error) {
          console.error(`AgentTaskCoordinator: Failed to dispatch task ${subtask.id}:`, error);
          await this.handleTaskError(subtask.id, subtask.agent, error instanceof Error ? error.message : String(error));
        }
      }
    }
  }

  /**
   * Handle task start notification
   */
  private handleTaskStart(taskId: string, agentId: string): void {
    console.log(`AgentTaskCoordinator: Task ${taskId} started on agent ${agentId}`);

    const taskResult = this.taskRegistry.get(taskId);
    if (taskResult) {
      taskResult.status = 'running';
      taskResult.startTime = Date.now();
    }

    // Notify callback
    if (this.callbacks.onTaskStart) {
      this.callbacks.onTaskStart(taskId, agentId);
    }
  }

  /**
   * Handle task completion and resolve dependencies
   */
  private async handleTaskComplete(taskId: string, agentId: string, result: AgentResponse): Promise<void> {
    console.log(`AgentTaskCoordinator: Task ${taskId} completed by agent ${agentId}`);

    const taskResult = this.taskRegistry.get(taskId);
    if (taskResult) {
      taskResult.status = 'completed';
      taskResult.completionTime = Date.now();
    }

    // Resolve dependencies for dependent tasks
    await this.resolveDependencies(taskId);

    // Notify callback
    if (this.callbacks.onTaskComplete) {
      this.callbacks.onTaskComplete(taskId, agentId, result);
    }
  }

  /**
   * Handle task error with retry logic
   */
  private async handleTaskError(taskId: string, agentId: string, error: string): Promise<void> {
    console.error(`AgentTaskCoordinator: Task ${taskId} failed on agent ${agentId}: ${error}`);

    const taskResult = this.taskRegistry.get(taskId);
    if (!taskResult) return;

    taskResult.retryCount++;
    taskResult.lastError = error;

    const willRetry = taskResult.retryCount < taskResult.maxRetries;

    if (willRetry) {
      console.log(`AgentTaskCoordinator: Retrying task ${taskId} (attempt ${taskResult.retryCount + 1}/${taskResult.maxRetries})`);
      taskResult.status = 'retrying';

      // Notify retry callback
      if (this.callbacks.onTaskError) {
        this.callbacks.onTaskError(taskId, agentId, error, true);
      }

      // Retry after a delay
      setTimeout(async () => {
        await this.retryTask(taskId);
      }, Math.pow(2, taskResult.retryCount) * 1000); // Exponential backoff

    } else {
      console.error(`AgentTaskCoordinator: Task ${taskId} failed permanently after ${taskResult.retryCount} attempts`);
      taskResult.status = 'failed';

      // Mark dependent tasks as failed too
      await this.failDependentTasks(taskId);

      // Notify final failure callback
      if (this.callbacks.onTaskFailed) {
        this.callbacks.onTaskFailed(taskId, agentId, error);
      }
    }
  }

  /**
   * Retry a failed task
   */
  private async retryTask(taskId: string): Promise<void> {
    const taskResult = this.taskRegistry.get(taskId);
    if (!taskResult) return;

    console.log(`AgentTaskCoordinator: Retrying task ${taskId}`);
    taskResult.status = 'ready';

    // Find the original subtask and retry
    // Note: In a real implementation, we'd need to store the original subtask data
    // For now, we'll just mark it as ready and let the coordination cycle pick it up
  }

  /**
   * Resolve dependencies when a task completes
   */
  private async resolveDependencies(completedTaskId: string): Promise<void> {
    const dependentTaskIds = this.dependencyGraph.get(completedTaskId);
    if (!dependentTaskIds) return;

    console.log(`AgentTaskCoordinator: Resolving dependencies for ${dependentTaskIds.size} dependent tasks`);

    for (const dependentTaskId of dependentTaskIds) {
      const dependentTask = this.taskRegistry.get(dependentTaskId);
      if (!dependentTask) continue;

      // Update dependency status
      dependentTask.dependencyStatus[completedTaskId] = 'completed';

      // Notify dependency resolved callback
      if (this.callbacks.onDependencyResolved) {
        this.callbacks.onDependencyResolved(dependentTaskId, completedTaskId);
      }

      // Check if all dependencies are resolved
      const allDependenciesResolved = dependentTask.dependencies.every(
        depId => dependentTask.dependencyStatus[depId] === 'completed'
      );

      if (allDependenciesResolved && dependentTask.status === 'waiting') {
        console.log(`AgentTaskCoordinator: Task ${dependentTaskId} is now ready (all dependencies resolved)`);
        dependentTask.status = 'ready';

        // Trigger processing of newly ready tasks
        // Note: In a real implementation, we'd need access to the original subtasks
        // For now, we'll rely on the coordination cycle
      }
    }
  }

  /**
   * Fail dependent tasks when a dependency fails
   */
  private async failDependentTasks(failedTaskId: string): Promise<void> {
    const dependentTaskIds = this.dependencyGraph.get(failedTaskId);
    if (!dependentTaskIds) return;

    console.log(`AgentTaskCoordinator: Failing ${dependentTaskIds.size} dependent tasks due to failed dependency ${failedTaskId}`);

    for (const dependentTaskId of dependentTaskIds) {
      const dependentTask = this.taskRegistry.get(dependentTaskId);
      if (!dependentTask) continue;

      dependentTask.status = 'failed';
      dependentTask.lastError = `Dependency ${failedTaskId} failed`;

      // Recursively fail tasks that depend on this one
      await this.failDependentTasks(dependentTaskId);
    }
  }

  /**
   * Start coordination monitoring
   */
  private startCoordination(): void {
    // Monitor coordination every 5 seconds
    this.coordinationInterval = setInterval(() => {
      this.monitorCoordination();
    }, 5000);
  }

  /**
   * Monitor coordination status
   */
  private monitorCoordination(): void {
    const stats = this.getCoordinationStats();
    
    if (stats.total > 0) {
      console.log(`AgentTaskCoordinator: Status - Total: ${stats.total}, Waiting: ${stats.waiting}, Ready: ${stats.ready}, Running: ${stats.running}, Completed: ${stats.completed}, Failed: ${stats.failed}, Retrying: ${stats.retrying}`);
    }
  }

  /**
   * Get coordination statistics
   */
  public getCoordinationStats(): {
    total: number;
    waiting: number;
    ready: number;
    running: number;
    completed: number;
    failed: number;
    retrying: number;
  } {
    const tasks = Array.from(this.taskRegistry.values());
    
    return {
      total: tasks.length,
      waiting: tasks.filter(t => t.status === 'waiting').length,
      ready: tasks.filter(t => t.status === 'ready').length,
      running: tasks.filter(t => t.status === 'running').length,
      completed: tasks.filter(t => t.status === 'completed').length,
      failed: tasks.filter(t => t.status === 'failed').length,
      retrying: tasks.filter(t => t.status === 'retrying').length
    };
  }

  /**
   * Get task status
   */
  public getTaskStatus(taskId: string): TaskCoordinationResult | null {
    return this.taskRegistry.get(taskId) || null;
  }

  /**
   * Get all task statuses
   */
  public getAllTaskStatuses(): TaskCoordinationResult[] {
    return Array.from(this.taskRegistry.values());
  }

  /**
   * Cleanup coordination
   */
  public cleanup(): void {
    if (this.coordinationInterval) {
      clearInterval(this.coordinationInterval);
      this.coordinationInterval = null;
    }
    this.taskRegistry.clear();
    this.dependencyGraph.clear();
  }
}
