// components/agents/micromanager-agent.ts
import { AgentBase, AgentConfig, AgentContext, AgentResponse } from './agent-base';
import { LLMRequestService, LLMMessage } from './llm-request-service';

export interface TaskDecomposition {
  subtasks: SubTask[];
  dependencies: TaskDependency[];
  estimatedComplexity: 'low' | 'medium' | 'high' | 'very_high';
  recommendedAgents: string[];
}

export interface SubTask {
  id: string;
  description: string;
  type: 'code_generation' | 'code_modification' | 'research' | 'architecture' | 'design' | 'testing';
  complexity: 'trivial' | 'simple' | 'moderate' | 'complex';
  estimatedTokens: number;
  requiredCapabilities: string[];
  context: Partial<AgentContext>;
}

export interface TaskDependency {
  taskId: string;
  dependsOn: string[];
  type: 'blocks' | 'enhances' | 'requires';
}

export class MicromanagerAgent extends AgentBase {
  private llmService: LLMRequestService;

  constructor(config: AgentConfig) {
    super(config);
    this.llmService = LLMRequestService.getInstance();
  }

  public getCapabilities(): string[] {
    return [
      'task_decomposition',
      'agent_coordination',
      'project_orchestration',
      'context_management',
      'dependency_resolution',
      'progress_tracking'
    ];
  }

  public getSystemPrompt(): string {
    return `You are the Micromanager, the central orchestrator of the Synapse AI coding system. Your responsibilities include:

1. TASK ORCHESTRATION:
   - Break down complex tasks into hierarchical subtasks with clear dependencies
   - Classify tasks by complexity, type, and required capabilities
   - Determine optimal agent assignments based on task requirements

2. AGENT DELEGATION:
   - Match tasks to agents based on complexity and specialization:
     * Intern: Simple, template-based tasks, boilerplate code
     * Junior: Single file implementations with clear specifications
     * MidLevel: Multiple related files, moderate complexity
     * Senior: Complex algorithms, architecture, integration challenges
     * Researcher: Context gathering, pattern identification
     * Architect: System design, component relationships
     * Designer: UI components, styling, brand compliance
   - Provide appropriate context for each subtask
   - Include clear success criteria and reporting requirements

3. COMMUNICATION PROTOCOLS:
   - Use structured message format: [AGENT:name] - [TASK:id] - [TYPE:type] - [MESSAGE]
   - Monitor progress and handle escalations
   - Coordinate between agents for complex tasks

4. ERROR HANDLING:
   - Implement escalation protocols for failed tasks
   - Coordinate with ErrorResolutionCoordinator for persistent issues
   - Adapt strategies based on agent performance

5. PROJECT COHERENCE:
   - Maintain consistency across all components
   - Track overall progress against project goals
   - Identify integration issues before they occur

Always provide task-specific context and clear expectations. Reference specific capabilities and constraints for optimal delegation.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      console.log(`🔍 MicromanagerAgent.execute: Starting execution for task: "${context.task}"`)
      console.log(`🔍 Context metadata:`, context.metadata)

      const validation = this.validateContext(context);
      if (!validation.valid) {
        console.log(`❌ MicromanagerAgent: Context validation failed: ${validation.error}`)
        return this.createErrorResponse(validation.error!);
      }

      // ✅ Check if agent has proper LLM configuration
      if (!this.config.provider || !this.config.model) {
        const error = `MicromanagerAgent: No valid LLM provider/model configured. Provider: ${this.config.provider}, Model: ${this.config.model}`;
        console.log(`❌ ${error}`)
        throw new Error(error);
      }

      console.log(`✅ MicromanagerAgent: Using provider: ${this.config.provider}, model: ${this.config.model}`)

      // ✅ For direct chat interactions, use LLM instead of orchestration
      if (context.metadata?.source === 'agent_chat') {
        console.log(`🔍 MicromanagerAgent: Detected chat interaction, routing to handleChatInteraction`)
        return await this.handleChatInteraction(context);
      }

      // For task orchestration, use the existing decomposition logic
      const analysis = await this.analyzeTask(context);
      const decomposition = await this.decomposeTask(context, analysis);
      const executionPlan = await this.createExecutionPlan(decomposition);

      // Generate orchestration response using LLM
      const response = await this.generateOrchestrationResponse(context, decomposition, executionPlan);

      const executionTime = Date.now() - startTime;
      const tokensUsed = this.estimateTokens(context) + 500;

      return this.createSuccessResponse(
        response,
        tokensUsed,
        executionTime,
        this.generateSuggestions(decomposition),
        {
          decomposition,
          executionPlan,
          analysis
        }
      );

    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Orchestration failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context),
        executionTime
      );
    }
  }

  /**
   * ✅ Handle direct chat interactions with real LLM calls
   * Replaces hardcoded simulation with actual AI responses
   * ✅ Task 80: Added LLM response caching for non-streaming interactions
   */
  private async handleChatInteraction(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      console.log(`🔍 MicromanagerAgent.handleChatInteraction: Starting LLM call for task: "${context.task}"`)
      console.log(`🔍 Using LLM config:`, { provider: this.config.provider, model: this.config.model })

      // Prepare messages for LLM - use a chat-focused system prompt
      const messages: LLMMessage[] = [
        {
          role: 'system',
          content: `You are the Micromanager, an AI assistant that helps coordinate and manage development tasks. You are knowledgeable, helpful, and can assist with:

- Breaking down complex tasks into manageable steps
- Providing guidance on development approaches
- Coordinating between different aspects of a project
- Offering technical advice and best practices
- Helping with planning and organization

Respond naturally and conversationally while being informative and helpful. Focus on providing practical, actionable advice.`
        },
        {
          role: 'user',
          content: context.task
        }
      ];

      // ✅ Task 74: Add chatMessageId logging for streaming trace
      const chatMessageId = context.metadata?.chatMessageId;
      console.log(`🔍 [Agent LLM Start] MicromanagerAgent: Starting LLM call for chatMessageId: "${chatMessageId}"`)
      console.log(`🔍 [Agent LLM Start] Prompt: "${context.task.substring(0, 100)}..."`)
      console.log(`🔍 [Agent LLM Start] Using provider: ${this.config.provider}, model: ${this.config.model}`)

      // ✅ Task 80: Generate context hash and check cache first
      const contextHash = this.generateContextHash(context);
      const taskId = context.metadata?.originalTaskId || `chat-${Date.now()}`;

      // Check if we have a cached response (skip for streaming chat interactions)
      if (!chatMessageId) {
        const cachedResponse = await this.getCachedLLMResponse(taskId);
        if (cachedResponse) {
          console.log(`🔍 MicromanagerAgent: Using cached response for task ${taskId}`);
          const executionTime = Date.now() - startTime;
          return this.createSuccessResponse(
            cachedResponse,
            0, // No tokens used for cached response
            executionTime,
            ['Response retrieved from cache', 'No LLM call required'],
            {
              cached: true,
              contextHash,
              chatInteraction: !!chatMessageId
            }
          );
        }
      }

      // ✅ CRITICAL FIX: Use streaming for chat interactions to enable real-time response
      let llmResponse: any;

      if (chatMessageId) {
        console.log(`🚀 [Agent LLM Start] Using STREAMING for chatMessageId: "${chatMessageId}"`)

        // Use streaming for real-time chat responses
        llmResponse = await this.llmService.callLLMStream(
          this.config,
          messages,
          (chunk) => {
            console.log(`📡 [Agent Stream Chunk] chatMessageId: "${chatMessageId}", delta: "${chunk.delta}", complete: ${chunk.isComplete}`)

            // ✅ Broadcast streaming chunk to frontend
            this.broadcastStreamingChunk(chatMessageId, chunk);
          }
        );

        console.log(`✅ [Agent LLM Complete] Streaming completed for chatMessageId: "${chatMessageId}"`, {
          provider: llmResponse.provider,
          model: llmResponse.model,
          tokensUsed: llmResponse.tokensUsed.total,
          contentLength: llmResponse.content.length,
          finishReason: llmResponse.finishReason
        })
      } else {
        console.log(`🔍 [Agent LLM Start] Using NON-STREAMING (no chatMessageId)`)

        // Use regular call for non-chat interactions
        llmResponse = await this.llmService.callLLM(this.config, messages);

        console.log(`✅ [Agent LLM Complete] Non-streaming completed`, {
          provider: llmResponse.provider,
          model: llmResponse.model,
          tokensUsed: llmResponse.tokensUsed.total,
          contentLength: llmResponse.content.length,
          finishReason: llmResponse.finishReason
        })
      }

      // ✅ Task 80: Cache the LLM response for future use (skip for streaming chat)
      if (!chatMessageId && llmResponse.content) {
        await this.cacheLLMResponse(
          taskId,
          llmResponse.content,
          llmResponse.tokensUsed.total,
          llmResponse.model,
          llmResponse.provider,
          llmResponse.finishReason,
          llmResponse.responseTime,
          contextHash,
          {
            chatInteraction: !!chatMessageId,
            timestamp: Date.now()
          }
        );
        console.log(`🔍 MicromanagerAgent: Cached response for task ${taskId}`);
      }

      // ✅ Task 82: Store context for other agents to access
      if (!chatMessageId && llmResponse.content) {
        await this.storeSharedContext(
          taskId,
          'plan_outline',
          {
            originalTask: context.task,
            response: llmResponse.content,
            analysis: 'micromanager_analysis',
            recommendations: 'micromanager_recommendations',
            complexity: 'determined_by_micromanager'
          },
          {
            tags: ['micromanager', 'planning', 'coordination'],
            metadata: {
              provider: llmResponse.provider,
              model: llmResponse.model,
              tokensUsed: llmResponse.tokensUsed.total,
              chatInteraction: !!chatMessageId
            }
          }
        );
        console.log(`🔍 MicromanagerAgent: Stored shared context for task ${taskId}`);
      }

      const executionTime = Date.now() - startTime;

      const response = this.createSuccessResponse(
        llmResponse.content,
        llmResponse.tokensUsed.total,
        executionTime,
        ['AI-generated response from Micromanager', 'Real LLM integration active'],
        {
          chatInteraction: true,
          provider: llmResponse.provider,
          model: llmResponse.model,
          finishReason: llmResponse.finishReason,
          responseTime: llmResponse.responseTime,
          contextHash,
          cached: false
        }
      );

      console.log(`✅ MicromanagerAgent: Created success response with content length: ${response.content?.length}`)
      return response;

    } catch (error) {
      console.error(`❌ MicromanagerAgent.handleChatInteraction failed:`, error)
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Chat interaction failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context),
        executionTime
      );
    }
  }

  /**
   * ✅ Task 74: Broadcast streaming chunks to frontend for real-time chat
   */
  private broadcastStreamingChunk(chatMessageId: string, chunk: any): void {
    try {
      console.log(`📡 [Agent Broadcast] Broadcasting chunk for chatMessageId: "${chatMessageId}"`)

      // ✅ Broadcast via Electron IPC if available
      if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
        window.electronAPI.ipc.send('agent-streaming-chunk', {
          type: 'agentMessageChunk',
          chatMessageId,
          content: chunk.content,
          delta: chunk.delta,
          isComplete: chunk.isComplete,
          tokensUsed: chunk.tokensUsed,
          finishReason: chunk.finishReason,
          timestamp: Date.now()
        });

        console.log(`✅ [Agent Broadcast] Chunk sent via IPC for chatMessageId: "${chatMessageId}"`)
      } else {
        console.warn(`⚠️ [Agent Broadcast] Electron API not available for chatMessageId: "${chatMessageId}"`)
      }

      // ✅ Also try to broadcast via agent manager if available
      if (typeof window !== 'undefined' && (window as any).agentManager) {
        (window as any).agentManager.broadcastMessage({
          agentId: this.config.id,
          type: 'streaming_chunk',
          message: chunk.delta,
          timestamp: Date.now(),
          metadata: {
            chatMessageId,
            content: chunk.content,
            isComplete: chunk.isComplete,
            tokensUsed: chunk.tokensUsed,
            finishReason: chunk.finishReason
          }
        });

        console.log(`✅ [Agent Broadcast] Chunk sent via agent manager for chatMessageId: "${chatMessageId}"`)
      }

    } catch (error) {
      console.error(`❌ [Agent Broadcast] Failed to broadcast chunk for chatMessageId: "${chatMessageId}":`, error);
    }
  }

  private async analyzeTask(context: AgentContext): Promise<{
    type: string;
    complexity: string;
    domain: string[];
    estimatedEffort: number;
    riskFactors: string[];
  }> {
    const task = context.task.toLowerCase();

    // Determine task type
    let type = 'other';
    if (task.includes('create') || task.includes('implement') || task.includes('build')) {
      type = 'code_generation';
    } else if (task.includes('fix') || task.includes('update') || task.includes('modify')) {
      type = 'code_modification';
    } else if (task.includes('analyze') || task.includes('research') || task.includes('find')) {
      type = 'research';
    } else if (task.includes('design') || task.includes('architecture') || task.includes('plan')) {
      type = 'architecture';
    } else if (task.includes('style') || task.includes('ui') || task.includes('interface')) {
      type = 'design';
    } else if (task.includes('test') || task.includes('validate')) {
      type = 'testing';
    }

    // Determine complexity
    let complexity = 'moderate';
    const complexityIndicators = {
      simple: ['simple', 'basic', 'straightforward', 'quick'],
      complex: ['complex', 'advanced', 'sophisticated', 'intricate', 'multiple', 'integration']
    };

    if (complexityIndicators.simple.some(indicator => task.includes(indicator))) {
      complexity = 'simple';
    } else if (complexityIndicators.complex.some(indicator => task.includes(indicator))) {
      complexity = 'complex';
    }

    // Identify domain areas
    const domains: string[] = [];
    const domainKeywords = {
      frontend: ['ui', 'interface', 'component', 'react', 'css', 'html'],
      backend: ['api', 'server', 'database', 'endpoint', 'service'],
      infrastructure: ['deploy', 'build', 'config', 'setup', 'install'],
      testing: ['test', 'spec', 'validate', 'verify', 'qa'],
      documentation: ['doc', 'readme', 'comment', 'guide', 'manual']
    };

    Object.entries(domainKeywords).forEach(([domain, keywords]) => {
      if (keywords.some(keyword => task.includes(keyword))) {
        domains.push(domain);
      }
    });

    // Estimate effort (1-10 scale)
    let estimatedEffort = 5;
    if (complexity === 'simple') estimatedEffort = 2;
    if (complexity === 'complex') estimatedEffort = 8;
    if (domains.length > 2) estimatedEffort += 2;

    // Identify risk factors
    const riskFactors: string[] = [];
    if (task.includes('database')) riskFactors.push('data_integrity');
    if (task.includes('security')) riskFactors.push('security_critical');
    if (task.includes('performance')) riskFactors.push('performance_sensitive');
    if (domains.length > 3) riskFactors.push('cross_domain_complexity');

    return {
      type,
      complexity,
      domain: domains,
      estimatedEffort,
      riskFactors
    };
  }

  private async decomposeTask(context: AgentContext, analysis: any): Promise<TaskDecomposition> {
    // For simple tasks, no decomposition needed
    if (analysis.complexity === 'simple') {
      return {
        subtasks: [{
          id: 'main_task',
          description: context.task,
          type: analysis.type,
          complexity: 'simple',
          estimatedTokens: this.estimateTokens(context),
          requiredCapabilities: this.getRequiredCapabilities(analysis),
          context: context
        }],
        dependencies: [],
        estimatedComplexity: 'low',
        recommendedAgents: this.recommendAgents(analysis, 'simple')
      };
    }

    // For complex tasks, break down into subtasks
    const subtasks: SubTask[] = [];
    const dependencies: TaskDependency[] = [];

    // Add research phase if needed
    if (analysis.type === 'code_generation' || analysis.complexity === 'complex') {
      subtasks.push({
        id: 'research_phase',
        description: `Research and analyze requirements for: ${context.task}`,
        type: 'research',
        complexity: 'simple',
        estimatedTokens: 1000,
        requiredCapabilities: ['codebase_analysis', 'pattern_recognition'],
        context: { ...context, task: `Research and analyze requirements for: ${context.task}` }
      });
    }

    // Add architecture phase for complex tasks
    if (analysis.complexity === 'complex' || analysis.domain.length > 2) {
      const archTaskId = 'architecture_phase';
      subtasks.push({
        id: archTaskId,
        description: `Design architecture for: ${context.task}`,
        type: 'architecture',
        complexity: 'moderate',
        estimatedTokens: 1500,
        requiredCapabilities: ['system_design', 'component_planning'],
        context: { ...context, task: `Design architecture for: ${context.task}` }
      });

      if (subtasks.length > 1) {
        dependencies.push({
          taskId: archTaskId,
          dependsOn: ['research_phase'],
          type: 'requires'
        });
      }
    }

    // Add implementation phases
    const implTaskId = 'implementation_phase';
    subtasks.push({
      id: implTaskId,
      description: `Implement: ${context.task}`,
      type: 'code_generation',
      complexity: analysis.complexity,
      estimatedTokens: this.estimateTokens(context),
      requiredCapabilities: ['code_generation', 'problem_solving'],
      context: context
    });

    // Add dependencies for implementation
    const prereqs = subtasks.filter(t => t.id !== implTaskId).map(t => t.id);
    if (prereqs.length > 0) {
      dependencies.push({
        taskId: implTaskId,
        dependsOn: prereqs,
        type: 'requires'
      });
    }

    // Add testing phase if needed
    if (analysis.type === 'code_generation' || analysis.riskFactors.length > 0) {
      const testTaskId = 'testing_phase';
      subtasks.push({
        id: testTaskId,
        description: `Test and validate: ${context.task}`,
        type: 'testing',
        complexity: 'simple',
        estimatedTokens: 800,
        requiredCapabilities: ['testing', 'validation'],
        context: { ...context, task: `Test and validate: ${context.task}` }
      });

      dependencies.push({
        taskId: testTaskId,
        dependsOn: [implTaskId],
        type: 'requires'
      });
    }

    return {
      subtasks,
      dependencies,
      estimatedComplexity: this.mapComplexity(analysis.complexity),
      recommendedAgents: this.recommendAgents(analysis, analysis.complexity)
    };
  }

  private async createExecutionPlan(decomposition: TaskDecomposition): Promise<{
    phases: { name: string; tasks: string[]; estimatedTime: number }[];
    totalEstimatedTime: number;
    criticalPath: string[];
    parallelizable: string[][];
  }> {
    const phases: { name: string; tasks: string[]; estimatedTime: number }[] = [];
    const completed = new Set<string>();
    const remaining = new Set(decomposition.subtasks.map(t => t.id));

    while (remaining.size > 0) {
      const ready = decomposition.subtasks.filter(task =>
        remaining.has(task.id) &&
        this.getDependencies(task.id, decomposition.dependencies)
          .every(dep => completed.has(dep))
      );

      if (ready.length === 0) break; // Circular dependency or error

      const phaseTime = Math.max(...ready.map(t => t.estimatedTokens / 100)); // Rough time estimate
      phases.push({
        name: `Phase ${phases.length + 1}`,
        tasks: ready.map(t => t.id),
        estimatedTime: phaseTime
      });

      ready.forEach(task => {
        completed.add(task.id);
        remaining.delete(task.id);
      });
    }

    const totalEstimatedTime = phases.reduce((sum, phase) => sum + phase.estimatedTime, 0);
    const criticalPath = this.findCriticalPath(decomposition);
    const parallelizable = phases.filter(p => p.tasks.length > 1).map(p => p.tasks);

    return { phases, totalEstimatedTime, criticalPath, parallelizable };
  }

  private async generateOrchestrationResponse(
    context: AgentContext,
    decomposition: TaskDecomposition,
    executionPlan: any
  ): Promise<string> {
    // ✅ Use LLM to generate intelligent orchestration response instead of hardcoded template
    try {
      const orchestrationData = {
        task: context.task,
        complexity: decomposition.estimatedComplexity,
        subtaskCount: decomposition.subtasks.length,
        phases: executionPlan.phases.length,
        estimatedTime: executionPlan.totalEstimatedTime,
        subtasks: decomposition.subtasks.map(st => ({
          id: st.id,
          description: st.description,
          type: st.type,
          complexity: st.complexity,
          agent: this.getRecommendedAgent(st)
        })),
        dependencies: decomposition.dependencies,
        recommendedAgents: decomposition.recommendedAgents
      };

      const messages: LLMMessage[] = [
        {
          role: 'system',
          content: `You are the Micromanager, creating an intelligent orchestration plan. Generate a comprehensive, well-structured response that explains how to approach the given task. Include:

1. Task analysis and approach
2. Recommended execution strategy
3. Key considerations and potential challenges
4. Next steps and coordination points

Be professional, detailed, and actionable. Focus on practical guidance rather than just listing data.`
        },
        {
          role: 'user',
          content: `Create an orchestration plan for this task:

TASK: ${context.task}

ANALYSIS DATA:
- Complexity: ${decomposition.estimatedComplexity}
- Subtasks identified: ${decomposition.subtasks.length}
- Execution phases: ${executionPlan.phases.length}
- Estimated time: ${executionPlan.totalEstimatedTime} minutes

SUBTASKS:
${decomposition.subtasks.map(st => `- ${st.description} (${st.type}, ${st.complexity})`).join('\n')}

RECOMMENDED AGENTS:
${decomposition.recommendedAgents.join(', ')}

Please provide a comprehensive orchestration plan with strategic guidance.`
        }
      ];

      // ✅ Call LLM service for intelligent orchestration with extended timeout for complex models
      const extendedTimeout = this.getTimeoutForProvider(this.config.provider);
      const llmResponse = await this.llmService.callLLM(this.config, messages, extendedTimeout);
      return llmResponse.content;

    } catch (error) {
      console.error('LLM orchestration failed, falling back to structured template:', error);

      // ✅ Enhanced error logging for debugging
      if (error instanceof Error) {
        if (error.message.includes('timed out')) {
          console.warn(`⏰ LLM timeout for ${this.config.provider}/${this.config.model} - consider increasing timeout or switching to faster model`);
        } else if (error.message.includes('deepseek')) {
          console.warn(`🔧 DeepSeek API issue - check API key and service status`);
        }
      }

      // Fallback to a basic structured response if LLM fails
      return `# Orchestration Plan for: ${context.task}

## Analysis
- **Complexity**: ${decomposition.estimatedComplexity}
- **Subtasks**: ${decomposition.subtasks.length}
- **Estimated Time**: ${executionPlan.totalEstimatedTime} minutes

## Execution Strategy
${decomposition.subtasks.map(st => `- **${st.description}** (${st.type}) - Assign to ${this.getRecommendedAgent(st)}`).join('\n')}

## Recommended Agents
${decomposition.recommendedAgents.join(', ')}

## Next Steps
1. Begin with the first phase tasks
2. Monitor progress and coordinate between agents
3. Handle any escalations or blocking issues

*Note: LLM orchestration temporarily unavailable, using structured fallback.*`;
    }
  }

  /**
   * ✅ Get appropriate timeout for different LLM providers
   */
  private getTimeoutForProvider(provider?: string): number {
    const baseTimeout = 30000; // 30 seconds default

    switch (provider) {
      case 'deepseek':
        return 60000; // 60 seconds for DeepSeek (can be slower)
      case 'anthropic':
        return 45000; // 45 seconds for Claude (generally reliable)
      case 'openai':
        return 30000; // 30 seconds for OpenAI (usually fast)
      case 'openrouter':
        return 45000; // 45 seconds for OpenRouter (varies by model)
      default:
        return baseTimeout;
    }
  }

  private getRequiredCapabilities(analysis: any): string[] {
    const capabilities: string[] = [];

    switch (analysis.type) {
      case 'code_generation':
        capabilities.push('code_generation', 'problem_solving');
        break;
      case 'code_modification':
        capabilities.push('code_modification', 'debugging');
        break;
      case 'research':
        capabilities.push('codebase_analysis', 'pattern_recognition');
        break;
      case 'architecture':
        capabilities.push('system_design', 'component_planning');
        break;
      case 'design':
        capabilities.push('ui_design', 'styling');
        break;
      case 'testing':
        capabilities.push('testing', 'validation');
        break;
    }

    if (analysis.domain.includes('frontend')) {
      capabilities.push('frontend_development');
    }
    if (analysis.domain.includes('backend')) {
      capabilities.push('backend_development');
    }

    return capabilities;
  }

  private recommendAgents(analysis: any, complexity: string): string[] {
    const agents: string[] = [];

    // Always include micromanager for coordination
    agents.push('micromanager');

    // Add research agent for complex tasks
    if (complexity === 'complex' || analysis.type === 'research') {
      agents.push('researcher');
    }

    // Add architect for system design
    if (analysis.type === 'architecture' || complexity === 'complex') {
      agents.push('architect');
    }

    // Add implementation agents based on complexity
    switch (complexity) {
      case 'simple':
        agents.push('intern', 'junior');
        break;
      case 'moderate':
        agents.push('junior', 'midlevel');
        break;
      case 'complex':
        agents.push('midlevel', 'senior');
        break;
    }

    // Add designer for UI tasks
    if (analysis.domain.includes('frontend') || analysis.type === 'design') {
      agents.push('designer');
    }

    return agents;
  }

  private getRecommendedAgent(subtask: SubTask): string {
    switch (subtask.type) {
      case 'research':
        return 'researcher';
      case 'architecture':
        return 'architect';
      case 'design':
        return 'designer';
      case 'testing':
        return 'junior'; // or dedicated tester agent
      case 'code_generation':
      case 'code_modification':
        switch (subtask.complexity) {
          case 'trivial':
          case 'simple':
            return 'intern';
          case 'moderate':
            return 'junior';
          case 'complex':
            return 'midlevel';
          default:
            return 'senior';
        }
      default:
        return 'junior';
    }
  }

  private mapComplexity(complexity: string): 'low' | 'medium' | 'high' | 'very_high' {
    switch (complexity) {
      case 'simple': return 'low';
      case 'moderate': return 'medium';
      case 'complex': return 'high';
      default: return 'very_high';
    }
  }

  private getDependencies(taskId: string, dependencies: TaskDependency[]): string[] {
    const dep = dependencies.find(d => d.taskId === taskId);
    return dep ? dep.dependsOn : [];
  }

  private findCriticalPath(decomposition: TaskDecomposition): string[] {
    // Simple critical path: longest chain of dependencies
    const visited = new Set<string>();
    const path: string[] = [];

    const findLongestPath = (taskId: string, currentPath: string[]): string[] => {
      if (visited.has(taskId)) return currentPath;

      visited.add(taskId);
      const newPath = [...currentPath, taskId];

      const dependents = decomposition.dependencies
        .filter(dep => dep.dependsOn.includes(taskId))
        .map(dep => dep.taskId);

      if (dependents.length === 0) return newPath;

      let longestPath = newPath;
      dependents.forEach(dependent => {
        const path = findLongestPath(dependent, newPath);
        if (path.length > longestPath.length) {
          longestPath = path;
        }
      });

      return longestPath;
    };

    // Find tasks with no dependencies (starting points)
    const startingTasks = decomposition.subtasks.filter(task =>
      !decomposition.dependencies.some(dep => dep.taskId === task.id)
    );

    let criticalPath: string[] = [];
    startingTasks.forEach(task => {
      visited.clear();
      const path = findLongestPath(task.id, []);
      if (path.length > criticalPath.length) {
        criticalPath = path;
      }
    });

    return criticalPath;
  }

  private generateSuggestions(decomposition: TaskDecomposition): string[] {
    const suggestions: string[] = [];

    if (decomposition.subtasks.length > 5) {
      suggestions.push('Consider breaking down further into smaller, more manageable tasks');
    }

    if (decomposition.dependencies.length > decomposition.subtasks.length) {
      suggestions.push('High dependency complexity detected - monitor for potential bottlenecks');
    }

    if (decomposition.estimatedComplexity === 'very_high') {
      suggestions.push('Very high complexity task - consider prototype or proof-of-concept first');
    }

    const parallelizable = decomposition.subtasks.filter(task =>
      !decomposition.dependencies.some(dep => dep.dependsOn.includes(task.id))
    );

    if (parallelizable.length > 1) {
      suggestions.push(`${parallelizable.length} tasks can be executed in parallel for faster completion`);
    }

    return suggestions;
  }
}