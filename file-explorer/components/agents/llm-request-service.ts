// components/agents/llm-request-service.ts
import { LLMProvider, getProviderConfig, getProviderModelId } from './llm-provider-registry';
import { AgentConfig } from './agent-base';
import { budgetEnforcer } from '../../lib/budget-enforcer';
import { BudgetExceededError, isBudgetExceededError } from '../../lib/budget-error';
import { CostSettings } from '../settings/settings-manager';
import { withStreamRecording } from '../debug/stream-recorder';

export interface LLMMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface LLMRequest {
  messages: LLMMessage[];
  model: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  taskId?: string; // For cost tracking
}

export interface LLMResponse {
  content: string;
  tokensUsed: {
    input: number;
    output: number;
    total: number;
  };
  model: string;
  provider: LLMProvider;
  finishReason: 'stop' | 'length' | 'content_filter' | 'error';
  cost?: number;
  responseTime: number;
  isStreaming?: boolean;
}

export interface StreamChunk {
  content: string;
  delta: string;
  isComplete: boolean;
  tokensUsed?: {
    input: number;
    output: number;
    total: number;
  };
  finishReason?: 'stop' | 'length' | 'content_filter' | 'error';
}

export type StreamCallback = (chunk: StreamChunk) => void;

export interface LLMError {
  code: string;
  message: string;
  provider: LLMProvider;
  retryable: boolean;
}

export class LLMRequestService {
  private static instance: LLMRequestService;
  private apiKeys: Map<LLMProvider, string> = new Map();
  private costSettings: CostSettings | null = null;

  private constructor() {}

  public static getInstance(): LLMRequestService {
    if (!LLMRequestService.instance) {
      LLMRequestService.instance = new LLMRequestService();
    }
    return LLMRequestService.instance;
  }

  public setApiKey(provider: LLMProvider, apiKey: string): void {
    this.apiKeys.set(provider, apiKey);
  }

  public getApiKey(provider: LLMProvider): string | undefined {
    return this.apiKeys.get(provider);
  }

  public hasAnyApiKey(): boolean {
    return this.apiKeys.size > 0;
  }

  public getConfiguredProviders(): LLMProvider[] {
    return Array.from(this.apiKeys.keys());
  }

  public logApiKeyStatus(): void {
    console.log('LLMRequestService API Key Status:', {
      totalKeys: this.apiKeys.size,
      providers: Array.from(this.apiKeys.keys()),
      hasOpenAI: this.apiKeys.has('openai'),
      hasAnthropic: this.apiKeys.has('anthropic'),
      hasOpenRouter: this.apiKeys.has('openrouter')
    });
  }

  public setCostSettings(costSettings: CostSettings): void {
    this.costSettings = costSettings;
  }

  public getCostSettings(): CostSettings | null {
    return this.costSettings;
  }

  public async validateApiKey(provider: LLMProvider, apiKey: string): Promise<boolean> {
    const config = getProviderConfig(provider);

    if (!config.keyValidationEndpoint) {
      // For providers without validation endpoints, assume valid if key exists
      return apiKey.length > 0;
    }

    try {
      // Use Electron IPC for API requests to bypass CORS
      if (typeof window !== 'undefined' && window.electronAPI?.llm) {
        return await window.electronAPI.llm.validateApiKey(provider, apiKey);
      }

      // Fallback for browser environment - basic validation
      console.warn(`Browser environment detected. Cannot validate ${provider} API key due to CORS restrictions.`);
      return apiKey.length > 0 && this.isValidApiKeyFormat(provider, apiKey);
    } catch (error) {
      console.error(`API key validation failed for ${provider}:`, error);
      return false;
    }
  }

  private isValidApiKeyFormat(provider: LLMProvider, apiKey: string): boolean {
    // Basic format validation for different providers
    switch (provider) {
      case 'openai':
        return apiKey.startsWith('sk-') && apiKey.length > 20;
      case 'anthropic':
        return apiKey.startsWith('sk-ant-') && apiKey.length > 20;
      case 'google':
        return apiKey.length > 20; // Google API keys don't have a specific prefix
      case 'azure':
        return apiKey.length > 20; // Azure keys vary in format
      case 'openrouter':
        return apiKey.startsWith('sk-or-') && apiKey.length > 20;
      case 'deepseek':
        return apiKey.startsWith('sk-') && apiKey.length > 20;
      case 'fireworks':
        return apiKey.length > 20; // Fireworks keys don't have a specific prefix
      default:
        return apiKey.length > 10;
    }
  }

  public static providerSupportsStreaming(provider: LLMProvider): boolean {
    return ['openai', 'openrouter', 'anthropic'].includes(provider);
  }

  public async callLLMStream(
    agent: AgentConfig,
    messages: LLMMessage[],
    onChunk: StreamCallback,
    options?: Partial<LLMRequest>
  ): Promise<LLMResponse> {
    const startTime = Date.now();

    if (!agent.provider) {
      throw new Error(`Agent ${agent.id} has no provider configured`);
    }

    if (!LLMRequestService.providerSupportsStreaming(agent.provider)) {
      console.warn(`Provider ${agent.provider} does not support streaming, falling back to regular call`);
      return this.callLLM(agent, messages, options);
    }

    const apiKey = this.getApiKey(agent.provider);
    if (!apiKey) {
      this.logApiKeyStatus();
      throw new Error(`No API key configured for provider: ${agent.provider}. Please configure an API key in Settings → API Keys.`);
    }

    const config = getProviderConfig(agent.provider);
    const modelId = getProviderModelId(agent.provider, agent.model || 'default');

    const request: LLMRequest = {
      messages,
      model: modelId,
      temperature: options?.temperature ?? agent.temperature ?? 0.7,
      maxTokens: options?.maxTokens ?? agent.maxTokens ?? 4000,
      stream: true,
      ...options
    };

    // Wrap the callback with stream recording for debugging
    const recordingCallback = withStreamRecording(agent, onChunk, options?.taskId);

    // Budget enforcement
    if (this.costSettings && budgetEnforcer.isBudgetEnforcementEnabled(this.costSettings)) {
      try {
        const inputText = messages.map(m => m.content).join(' ');
        const estimatedInputTokens = Math.ceil(inputText.length / 4);
        const estimatedOutputTokens = request.maxTokens || 1000;

        budgetEnforcer.enforceBudget(
          agent.provider,
          modelId,
          estimatedInputTokens,
          estimatedOutputTokens,
          this.costSettings
        );
      } catch (error) {
        if (isBudgetExceededError(error)) {
          console.error(`💸 Budget exceeded for ${agent.provider}/${modelId}:`, error.getUserMessage());
          throw error;
        }
        throw error;
      }
    }

    try {
      // Use Electron IPC for streaming if available
      if (typeof window !== 'undefined' && window.electronAPI?.llm?.callLLMStream) {
        return await window.electronAPI.llm.callLLMStream(agent.provider, request, apiKey, recordingCallback);
      }

      // Fallback to browser streaming
      return await this.streamResponse(agent.provider, config, apiKey, request, recordingCallback, startTime);

    } catch (error) {
      console.error(`LLM streaming request failed for ${agent.provider}:`, error);
      throw this.createLLMError(agent.provider, error);
    }
  }

  public async callLLM(
    agent: AgentConfig,
    messages: LLMMessage[],
    timeoutMs?: number,
    options?: Partial<LLMRequest>
  ): Promise<LLMResponse> {
    const startTime = Date.now();

    if (!agent.provider) {
      throw new Error(`Agent ${agent.id} has no provider configured`);
    }

    const apiKey = this.getApiKey(agent.provider);
    if (!apiKey) {
      this.logApiKeyStatus();
      throw new Error(`No API key configured for provider: ${agent.provider}. Please configure an API key in Settings → API Keys.`);
    }

    const config = getProviderConfig(agent.provider);
    const modelId = getProviderModelId(agent.provider, agent.model || 'default');

    const request: LLMRequest = {
      messages,
      model: modelId,
      temperature: options?.temperature ?? agent.temperature ?? 0.7,
      maxTokens: options?.maxTokens ?? agent.maxTokens ?? 4000,
      stream: false,
      ...options
    };

    // ✅ Budget enforcement - check before making the request
    if (this.costSettings && budgetEnforcer.isBudgetEnforcementEnabled(this.costSettings)) {
      try {
        // Estimate input tokens (rough approximation: 4 chars per token)
        const inputText = messages.map(m => m.content).join(' ');
        const estimatedInputTokens = Math.ceil(inputText.length / 4);
        const estimatedOutputTokens = request.maxTokens || 1000;

        // Enforce budget before making the request
        budgetEnforcer.enforceBudget(
          agent.provider,
          modelId,
          estimatedInputTokens,
          estimatedOutputTokens,
          this.costSettings
        );
      } catch (error) {
        if (isBudgetExceededError(error)) {
          console.error(`💸 Budget exceeded for ${agent.provider}/${modelId}:`, error.getUserMessage());
          throw error;
        }
        // Re-throw other errors
        throw error;
      }
    }

    try {
      // Use Electron IPC for LLM requests to bypass CORS
      if (typeof window !== 'undefined' && window.electronAPI?.llm) {
        const response = await window.electronAPI.llm.callLLM(agent.provider, request, apiKey, timeoutMs);
        const responseTime = Date.now() - startTime;

        const llmResponse: LLMResponse = {
          content: response.content,
          tokensUsed: response.tokensUsed,
          model: response.model,
          provider: agent.provider,
          finishReason: response.finishReason,
          responseTime
        };

        // ✅ Record actual cost after successful request
        if (this.costSettings && budgetEnforcer.isBudgetEnforcementEnabled(this.costSettings)) {
          try {
            await budgetEnforcer.recordCost(
              agent.provider,
              modelId,
              llmResponse.tokensUsed.input,
              llmResponse.tokensUsed.output,
              this.costSettings, // Pass cost settings for threshold checking
              agent.id,
              options?.taskId // Pass task ID if available in options
            );
          } catch (costError) {
            console.error('Failed to record cost:', costError);
            // Don't fail the LLM call if cost recording fails
          }
        }

        return llmResponse;
      }

      // Fallback for browser environment - throw error
      throw new Error(`Browser environment detected. LLM requests require Electron environment to bypass CORS restrictions.`);

    } catch (error) {
      console.error(`LLM request failed for ${agent.provider}:`, error);
      throw this.createLLMError(agent.provider, error);
    }
  }

  private async makeOpenAIRequest(
    provider: LLMProvider,
    config: any,
    apiKey: string,
    request: LLMRequest
  ): Promise<Response> {
    const payload = {
      model: request.model,
      messages: request.messages,
      temperature: request.temperature,
      max_tokens: request.maxTokens,
      stream: request.stream
    };

    return fetch(config.apiUrl, {
      method: 'POST',
      headers: config.headers(apiKey),
      body: JSON.stringify(payload)
    });
  }

  private async makeAnthropicRequest(
    provider: LLMProvider,
    config: any,
    apiKey: string,
    request: LLMRequest
  ): Promise<Response> {
    // Convert OpenAI format to Anthropic format
    const systemMessage = request.messages.find(m => m.role === 'system');
    const conversationMessages = request.messages.filter(m => m.role !== 'system');

    const payload = {
      model: request.model,
      max_tokens: request.maxTokens,
      temperature: request.temperature,
      system: systemMessage?.content,
      messages: conversationMessages.map(msg => ({
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content
      }))
    };

    return fetch(config.apiUrl, {
      method: 'POST',
      headers: config.headers(apiKey),
      body: JSON.stringify(payload)
    });
  }

  private async makeCustomRequest(
    provider: LLMProvider,
    config: any,
    apiKey: string,
    request: LLMRequest
  ): Promise<Response> {
    // Handle Google AI and other custom formats
    if (provider === 'google') {
      const url = config.apiUrl.replace('{model}', request.model) + `?key=${apiKey}`;

      const payload = {
        contents: [{
          parts: [{
            text: request.messages.map(m => `${m.role}: ${m.content}`).join('\n\n')
          }]
        }],
        generationConfig: {
          temperature: request.temperature,
          maxOutputTokens: request.maxTokens
        }
      };

      return fetch(url, {
        method: 'POST',
        headers: config.headers(apiKey),
        body: JSON.stringify(payload)
      });
    }

    throw new Error(`Custom request format not implemented for provider: ${provider}`);
  }

  private parseResponse(provider: LLMProvider, data: any, responseTime: number): LLMResponse {
    const config = getProviderConfig(provider);

    switch (config.responseFormat) {
      case 'openai':
        return this.parseOpenAIResponse(provider, data, responseTime);
      case 'anthropic':
        return this.parseAnthropicResponse(provider, data, responseTime);
      case 'custom':
        return this.parseCustomResponse(provider, data, responseTime);
      default:
        throw new Error(`Unsupported response format: ${config.responseFormat}`);
    }
  }

  private parseOpenAIResponse(provider: LLMProvider, data: any, responseTime: number): LLMResponse {
    const choice = data.choices?.[0];
    const usage = data.usage;

    return {
      content: choice?.message?.content || '',
      tokensUsed: {
        input: usage?.prompt_tokens || 0,
        output: usage?.completion_tokens || 0,
        total: usage?.total_tokens || 0
      },
      model: data.model,
      provider,
      finishReason: choice?.finish_reason || 'stop',
      responseTime
    };
  }

  private parseAnthropicResponse(provider: LLMProvider, data: any, responseTime: number): LLMResponse {
    const content = data.content?.[0]?.text || '';
    const usage = data.usage;

    return {
      content,
      tokensUsed: {
        input: usage?.input_tokens || 0,
        output: usage?.output_tokens || 0,
        total: (usage?.input_tokens || 0) + (usage?.output_tokens || 0)
      },
      model: data.model,
      provider,
      finishReason: data.stop_reason || 'stop',
      responseTime
    };
  }

  private parseCustomResponse(provider: LLMProvider, data: any, responseTime: number): LLMResponse {
    if (provider === 'google') {
      const candidate = data.candidates?.[0];
      const content = candidate?.content?.parts?.[0]?.text || '';

      return {
        content,
        tokensUsed: {
          input: data.usageMetadata?.promptTokenCount || 0,
          output: data.usageMetadata?.candidatesTokenCount || 0,
          total: data.usageMetadata?.totalTokenCount || 0
        },
        model: data.model || 'gemini-pro',
        provider,
        finishReason: candidate?.finishReason?.toLowerCase() || 'stop',
        responseTime
      };
    }

    throw new Error(`Custom response parsing not implemented for provider: ${provider}`);
  }

  private createLLMError(provider: LLMProvider, error: any): LLMError {
    return {
      code: error.code || 'UNKNOWN_ERROR',
      message: error.message || 'Unknown error occurred',
      provider,
      retryable: this.isRetryableError(error)
    };
  }

  private async streamResponse(
    provider: LLMProvider,
    config: any,
    apiKey: string,
    request: LLMRequest,
    onChunk: StreamCallback,
    startTime: number
  ): Promise<LLMResponse> {
    let response: Response;
    let fullContent = '';
    let tokensUsed = { input: 0, output: 0, total: 0 };
    let finishReason: 'stop' | 'length' | 'content_filter' | 'error' = 'stop';

    try {
      // Make streaming request based on provider
      switch (config.requestFormat) {
        case 'openai':
          response = await this.makeOpenAIRequest(provider, config, apiKey, request);
          break;
        case 'anthropic':
          response = await this.makeAnthropicStreamRequest(provider, config, apiKey, request);
          break;
        default:
          throw new Error(`Streaming not supported for request format: ${config.requestFormat}`);
      }

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Response body is not readable');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.trim() === '') continue;

            try {
              const chunk = this.parseStreamChunk(provider, line, fullContent);
              if (chunk) {
                fullContent = chunk.content;
                if (chunk.tokensUsed) {
                  tokensUsed = chunk.tokensUsed;
                }
                if (chunk.finishReason) {
                  finishReason = chunk.finishReason;
                }

                onChunk(chunk);

                if (chunk.isComplete) {
                  break;
                }
              }
            } catch (parseError) {
              console.warn('Failed to parse stream chunk:', parseError);
              // Continue processing other chunks
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      // Send final completion chunk
      onChunk({
        content: fullContent,
        delta: '',
        isComplete: true,
        tokensUsed,
        finishReason
      });

      const responseTime = Date.now() - startTime;

      // Record cost if budget enforcement is enabled
      if (this.costSettings && budgetEnforcer.isBudgetEnforcementEnabled(this.costSettings)) {
        try {
          await budgetEnforcer.recordCost(
            provider,
            request.model,
            tokensUsed.input,
            tokensUsed.output,
            this.costSettings,
            undefined, // agentId not available here
            request.taskId
          );
        } catch (costError) {
          console.error('Failed to record streaming cost:', costError);
        }
      }

      return {
        content: fullContent,
        tokensUsed,
        model: request.model,
        provider,
        finishReason,
        responseTime,
        isStreaming: true
      };

    } catch (error) {
      console.error('Streaming failed:', error);
      throw error;
    }
  }

  private parseStreamChunk(provider: LLMProvider, line: string, currentContent: string): StreamChunk | null {
    try {
      // Handle Server-Sent Events format
      if (line.startsWith('data: ')) {
        const data = line.slice(6);

        if (data === '[DONE]') {
          return {
            content: currentContent,
            delta: '',
            isComplete: true
          };
        }

        const parsed = JSON.parse(data);

        switch (provider) {
          case 'openai':
          case 'openrouter':
            return this.parseOpenAIStreamChunk(parsed, currentContent);
          case 'anthropic':
            return this.parseAnthropicStreamChunk(parsed, currentContent);
          default:
            return null;
        }
      }

      return null;
    } catch (error) {
      console.warn('Failed to parse stream chunk:', error);
      return null;
    }
  }

  private parseOpenAIStreamChunk(data: any, currentContent: string): StreamChunk | null {
    const choice = data.choices?.[0];
    if (!choice) return null;

    const delta = choice.delta?.content || '';
    const newContent = currentContent + delta;
    const isComplete = choice.finish_reason !== null;

    return {
      content: newContent,
      delta,
      isComplete,
      finishReason: choice.finish_reason || undefined,
      tokensUsed: data.usage ? {
        input: data.usage.prompt_tokens || 0,
        output: data.usage.completion_tokens || 0,
        total: data.usage.total_tokens || 0
      } : undefined
    };
  }

  private parseAnthropicStreamChunk(data: any, currentContent: string): StreamChunk | null {
    if (data.type === 'content_block_delta') {
      const delta = data.delta?.text || '';
      const newContent = currentContent + delta;

      return {
        content: newContent,
        delta,
        isComplete: false
      };
    }

    if (data.type === 'message_stop') {
      return {
        content: currentContent,
        delta: '',
        isComplete: true,
        finishReason: data.stop_reason || 'stop'
      };
    }

    if (data.type === 'message_delta' && data.usage) {
      return {
        content: currentContent,
        delta: '',
        isComplete: false,
        tokensUsed: {
          input: data.usage.input_tokens || 0,
          output: data.usage.output_tokens || 0,
          total: (data.usage.input_tokens || 0) + (data.usage.output_tokens || 0)
        }
      };
    }

    return null;
  }

  private async makeAnthropicStreamRequest(
    provider: LLMProvider,
    config: any,
    apiKey: string,
    request: LLMRequest
  ): Promise<Response> {
    const systemMessage = request.messages.find(m => m.role === 'system');
    const conversationMessages = request.messages.filter(m => m.role !== 'system');

    const payload = {
      model: request.model,
      max_tokens: request.maxTokens,
      temperature: request.temperature,
      system: systemMessage?.content,
      messages: conversationMessages.map(msg => ({
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content
      })),
      stream: true
    };

    return fetch(config.apiUrl, {
      method: 'POST',
      headers: {
        ...config.headers(apiKey),
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(payload)
    });
  }

  private isRetryableError(error: any): boolean {
    const retryableCodes = ['RATE_LIMIT', 'TIMEOUT', 'NETWORK_ERROR', 'SERVER_ERROR'];
    return retryableCodes.some(code => error.message?.includes(code));
  }
}
