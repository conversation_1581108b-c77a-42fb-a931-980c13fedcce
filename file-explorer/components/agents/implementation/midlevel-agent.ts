// components/agents/implementation/midlevel-agent.ts
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Agent<PERSON><PERSON>x<PERSON>, AgentResponse } from '../agent-base';

export interface FileModification {
  path: string;
  action: 'create' | 'modify' | 'delete';
  content?: string;
  changes?: { line: number; type: 'add' | 'remove' | 'modify'; content: string }[];
}

export class MidLevelAgent extends AgentBase {
  private maxRetryAttempts = 5;

  constructor(config: AgentConfig) {
    super(config);
  }

  public getCapabilities(): string[] {
    return [
      'multi_file_implementation',
      'component_integration',
      'api_design',
      'state_management',
      'database_integration',
      'testing_strategies',
      'performance_optimization',
      'cross_cutting_concerns',
      'design_patterns',
      'refactoring'
    ];
  }

  public getSystemPrompt(): string {
    return `You are the MidLevel implementation agent, responsible for implementing features across multiple related files while maintaining system coherence.

CORE RESPONSIBILITIES:
1. TASK EXECUTION:
   - Implement moderately complex features that span multiple related files
   - Analyze relationships between affected files before implementation
   - Plan implementation strategy with clear file touchpoints
   - Ensure consistency across modified files
   - Apply appropriate design patterns
   - Balance flexibility with simplicity
   - Consider performance implications

2. CONTEXT UNDERSTANDING:
   - Build a mental model of component interactions
   - Identify dependencies between modified files
   - Consider the impact of changes on other system parts
   - Reference architectural guidelines for modifications

3. COORDINATION:
   - Ensure consistent naming and patterns across files
   - Track changes across different components
   - Maintain API contracts between components
   - Document cross-file relationships

4. PROBLEM SOLVING:
   - Break down complex challenges into smaller components
   - Test each component individually
   - Consider alternative implementation approaches
   - Document trade-offs in chosen approach

5. ERROR MANAGEMENT:
   - Implement comprehensive error handling
   - Add appropriate logging across components
   - Create integration tests for cross-file functionality
   - Document potential failure modes

TASK TYPES YOU HANDLE:
- Multi-file feature implementations
- Component integration projects
- API development with multiple endpoints
- State management across components
- Database integration with multiple models
- Performance optimization projects
- Refactoring across multiple files
- Design pattern implementations
- Cross-cutting concern implementations

ESCALATION CRITERIA:
- Task requires complex architectural changes
- Significant performance optimization needed
- Implementation affects core system components
- After multiple failed attempts at solving complex issues

Focus on implementing cohesive functionality across multiple files while maintaining system integrity.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();
    let attemptCount = 0;
    let lastError: string | null = null;

    while (attemptCount < this.maxRetryAttempts) {
      try {
        attemptCount++;

        const validation = this.validateContext(context);
        if (!validation.valid) {
          return this.createErrorResponse(validation.error!);
        }

        // Analyze the multi-file task
        const taskAnalysis = this.analyzeMultiFileTask(context);

        // Check if task complexity requires escalation
        const complexityCheck = this.checkTaskComplexity(taskAnalysis);
        if (!complexityCheck.suitable) {
          return this.createErrorResponse(
            `Task complexity may require Senior agent: ${complexityCheck.reason}`
          );
        }

        // Plan the implementation across files
        const implementationPlan = await this.planImplementation(context, taskAnalysis);

        // Execute the implementation plan
        const fileModifications = await this.executeImplementationPlan(implementationPlan, context);

        // Validate the complete implementation
        const validation_result = this.validateMultiFileImplementation(fileModifications, taskAnalysis);
        if (!validation_result.valid && attemptCount < this.maxRetryAttempts) {
          lastError = validation_result.error!;
          continue; // Retry with different approach
        }

        // Generate integration tests
        const integrationTests = this.generateIntegrationTests(fileModifications, taskAnalysis);

        const executionTime = Date.now() - startTime;
        const tokensUsed = this.estimateTokens(context) + 600; // MidLevel processing overhead

        const response = this.formatMultiFileResponse(fileModifications, integrationTests, taskAnalysis);

        return this.createSuccessResponse(
          response,
          tokensUsed,
          executionTime,
          this.generateSuggestions(taskAnalysis, attemptCount > 1),
          {
            taskType: taskAnalysis.type,
            complexity: taskAnalysis.complexity,
            attempts: attemptCount,
            filesModified: fileModifications.length,
            integrationPoints: taskAnalysis.integrationPoints,
            patterns: taskAnalysis.patterns
          }
        );

      } catch (error) {
        lastError = error instanceof Error ? error.message : String(error);
        if (attemptCount >= this.maxRetryAttempts) {
          break;
        }
        // Continue to next attempt
      }
    }

    const executionTime = Date.now() - startTime;
    return this.createErrorResponse(
      `MidLevel execution failed after ${attemptCount} attempts. Last error: ${lastError}. Consider escalating to Senior agent.`,
      this.estimateTokens(context)
    );
  }

  private analyzeMultiFileTask(context: AgentContext): {
    type: string;
    complexity: string;
    affectedFiles: string[];
    integrationPoints: string[];
    patterns: string[];
    dependencies: string[];
    estimatedEffort: number;
  } {
    const task = context.task.toLowerCase();

    // Determine task type
    let type = 'multi_file_implementation';
    if (task.includes('integrate') || task.includes('connect')) type = 'integration';
    else if (task.includes('refactor') || task.includes('restructure')) type = 'refactoring';
    else if (task.includes('api') && task.includes('multiple')) type = 'api_development';
    else if (task.includes('state') && task.includes('management')) type = 'state_management';
    else if (task.includes('database') && task.includes('model')) type = 'database_integration';
    else if (task.includes('feature') && task.includes('complete')) type = 'feature_implementation';

    // Determine complexity
    let complexity = 'moderate';
    if (task.includes('simple') || task.includes('basic')) {
      complexity = 'simple';
    } else if (task.includes('complex') || task.includes('advanced') || task.includes('sophisticated')) {
      complexity = 'complex';
    }

    // Identify affected files
    const affectedFiles = context.files || [];
    if (affectedFiles.length === 0) {
      // Estimate based on task description
      if (task.includes('component')) affectedFiles.push('component.tsx', 'component.test.tsx');
      if (task.includes('api')) affectedFiles.push('routes.ts', 'controller.ts', 'service.ts');
      if (task.includes('database')) affectedFiles.push('model.ts', 'repository.ts', 'migration.ts');
    }

    // Identify integration points
    const integrationPoints: string[] = [];
    if (task.includes('frontend') && task.includes('backend')) integrationPoints.push('frontend-backend');
    if (task.includes('database')) integrationPoints.push('database');
    if (task.includes('api')) integrationPoints.push('api');
    if (task.includes('state')) integrationPoints.push('state-management');
    if (task.includes('component')) integrationPoints.push('component-integration');

    // Identify applicable patterns
    const patterns: string[] = [];
    if (task.includes('mvc')) patterns.push('model-view-controller');
    if (task.includes('repository')) patterns.push('repository-pattern');
    if (task.includes('factory')) patterns.push('factory-pattern');
    if (task.includes('observer')) patterns.push('observer-pattern');
    if (task.includes('decorator')) patterns.push('decorator-pattern');
    if (type === 'state_management') patterns.push('flux-pattern', 'redux-pattern');

    // Identify dependencies
    const dependencies: string[] = [];
    if (task.includes('react')) dependencies.push('react');
    if (task.includes('express')) dependencies.push('express');
    if (task.includes('database')) dependencies.push('database-driver');
    if (task.includes('test')) dependencies.push('testing-framework');

    // Estimate effort (1-10 scale)
    let estimatedEffort = 6;
    if (complexity === 'simple') estimatedEffort = 4;
    if (complexity === 'complex') estimatedEffort = 8;
    estimatedEffort += Math.min(affectedFiles.length * 0.5, 3);
    estimatedEffort += integrationPoints.length * 0.5;

    return {
      type,
      complexity,
      affectedFiles,
      integrationPoints,
      patterns,
      dependencies,
      estimatedEffort
    };
  }

  private checkTaskComplexity(analysis: any): { suitable: boolean; reason?: string } {
    // Check for indicators that require Senior agent
    const seniorIndicators = [
      'architecture', 'system design', 'performance critical', 'distributed',
      'microservice', 'scalability', 'complex algorithm'
    ];

    for (const indicator of seniorIndicators) {
      if (analysis.type.includes(indicator) || analysis.complexity === 'very_complex') {
        return {
          suitable: false,
          reason: `Task requires Senior-level expertise: ${indicator}`
        };
      }
    }

    // Check file count
    if (analysis.affectedFiles.length > 8) {
      return {
        suitable: false,
        reason: 'Too many files affected - consider Senior agent'
      };
    }

    // Check integration complexity
    if (analysis.integrationPoints.length > 4) {
      return {
        suitable: false,
        reason: 'High integration complexity - consider Senior agent'
      };
    }

    return { suitable: true };
  }

  private async planImplementation(context: AgentContext, analysis: any): Promise<{
    phases: { name: string; files: string[]; dependencies: string[] }[];
    fileOperations: { file: string; operation: string; priority: number }[];
    integrationStrategy: string;
  }> {
    const phases: { name: string; files: string[]; dependencies: string[] }[] = [];
    const fileOperations: { file: string; operation: string; priority: number }[] = [];

    // Phase 1: Foundation files (models, types, interfaces)
    const foundationFiles = analysis.affectedFiles.filter((file: string) => 
      file.includes('type') || file.includes('interface') || file.includes('model')
    );
    if (foundationFiles.length > 0) {
      phases.push({
        name: 'Foundation',
        files: foundationFiles,
        dependencies: []
      });
    }

    // Phase 2: Core implementation (services, utilities)
    const coreFiles = analysis.affectedFiles.filter((file: string) => 
      file.includes('service') || file.includes('util') || file.includes('helper')
    );
    if (coreFiles.length > 0) {
      phases.push({
        name: 'Core Implementation',
        files: coreFiles,
        dependencies: foundationFiles
      });
    }

    // Phase 3: API/Controller layer
    const apiFiles = analysis.affectedFiles.filter((file: string) => 
      file.includes('controller') || file.includes('route') || file.includes('api')
    );
    if (apiFiles.length > 0) {
      phases.push({
        name: 'API Layer',
        files: apiFiles,
        dependencies: [...foundationFiles, ...coreFiles]
      });
    }

    // Phase 4: UI/Component layer
    const uiFiles = analysis.affectedFiles.filter((file: string) => 
      file.includes('component') || file.includes('page') || file.includes('view')
    );
    if (uiFiles.length > 0) {
      phases.push({
        name: 'UI Layer',
        files: uiFiles,
        dependencies: [...foundationFiles, ...coreFiles, ...apiFiles]
      });
    }

    // Phase 5: Tests and documentation
    const testFiles = analysis.affectedFiles.filter((file: string) => 
      file.includes('test') || file.includes('spec')
    );
    if (testFiles.length > 0) {
      phases.push({
        name: 'Testing',
        files: testFiles,
        dependencies: analysis.affectedFiles.filter((f: string) => !f.includes('test') && !f.includes('spec'))
      });
    }

    // Create file operations with priorities
    phases.forEach((phase, phaseIndex) => {
      phase.files.forEach(file => {
        fileOperations.push({
          file,
          operation: this.determineFileOperation(file, context),
          priority: phaseIndex + 1
        });
      });
    });

    // Determine integration strategy
    let integrationStrategy = 'sequential';
    if (analysis.integrationPoints.length > 2) {
      integrationStrategy = 'parallel-with-sync-points';
    } else if (analysis.type === 'feature_implementation') {
      integrationStrategy = 'feature-branch';
    }

    return { phases, fileOperations, integrationStrategy };
  }

  private determineFileOperation(file: string, context: AgentContext): string {
    if (context.files?.includes(file)) {
      return 'modify';
    }
    return 'create';
  }

  private async executeImplementationPlan(plan: any, context: AgentContext): Promise<FileModification[]> {
    const modifications: FileModification[] = [];

    // Execute phases in order
    for (const phase of plan.phases) {
      for (const file of phase.files) {
        const operation = plan.fileOperations.find((op: any) => op.file === file);
        if (!operation) continue;

        const modification = await this.createFileModification(file, operation.operation, context);
        modifications.push(modification);
      }
    }

    return modifications;
  }

  private async createFileModification(filePath: string, operation: string, context: AgentContext): Promise<FileModification> {
    const modification: FileModification = {
      path: filePath,
      action: operation as 'create' | 'modify' | 'delete'
    };

    if (operation === 'create' || operation === 'modify') {
      modification.content = await this.generateFileContent(filePath, context);
    }

    return modification;
  }

  private async generateFileContent(filePath: string, context: AgentContext): Promise<string> {
    const fileType = this.determineFileType(filePath);
    
    let content = '';
    content += this.generateFileHeader(filePath, context);
    content += this.generateImports(fileType, filePath);
    
    switch (fileType) {
      case 'component':
        content += this.generateComponentContent(filePath, context);
        break;
      case 'service':
        content += this.generateServiceContent(filePath, context);
        break;
      case 'controller':
        content += this.generateControllerContent(filePath, context);
        break;
      case 'model':
        content += this.generateModelContent(filePath, context);
        break;
      case 'types':
        content += this.generateTypesContent(filePath, context);
        break;
      case 'test':
        content += this.generateTestContent(filePath, context);
        break;
      default:
        content += this.generateGenericContent(filePath, context);
    }

    return content;
  }

  private determineFileType(filePath: string): string {
    if (filePath.includes('component') || filePath.endsWith('.tsx')) return 'component';
    if (filePath.includes('service')) return 'service';
    if (filePath.includes('controller')) return 'controller';
    if (filePath.includes('model')) return 'model';
    if (filePath.includes('type') || filePath.includes('interface')) return 'types';
    if (filePath.includes('test') || filePath.includes('spec')) return 'test';
    return 'generic';
  }

  private generateFileHeader(filePath: string, context: AgentContext): string {
    return `/**
 * ${filePath}
 * 
 * Generated by MidLevel Agent
 * Task: ${context.task}
 * 
 * This file is part of a multi-file implementation
 * Dependencies: Check implementation plan for integration points
 */

`;
  }

  private generateImports(fileType: string, filePath: string): string {
    let imports = '';

    switch (fileType) {
      case 'component':
        imports += "import React, { useState, useEffect, useCallback } from 'react';\n";
        imports += "// Import related types and services\n";
        break;
      case 'service':
        imports += "// Import interfaces and types\n";
        imports += "// Import external dependencies\n";
        break;
      case 'controller':
        imports += "import { Request, Response, NextFunction } from 'express';\n";
        imports += "// Import services and models\n";
        break;
      case 'model':
        imports += "// Import database connection and types\n";
        break;
      case 'test':
        imports += "// Import testing framework\n";
        imports += "// Import components/services to test\n";
        break;
    }

    return imports + '\n';
  }

  private generateComponentContent(filePath: string, context: AgentContext): string {
    const componentName = this.extractNameFromPath(filePath) || 'Component';
    
    return `interface ${componentName}Props {
  className?: string;
  children?: React.ReactNode;
  // TODO: Add specific props based on requirements
}

export const ${componentName}: React.FC<${componentName}Props> = ({
  className,
  children,
  ...props
}) => {
  // State management
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Effects
  useEffect(() => {
    // TODO: Implement component initialization
  }, []);

  // Handlers
  const handleAction = useCallback(() => {
    // TODO: Implement action handlers
  }, []);

  return (
    <div className={className} {...props}>
      {loading && <div>Loading...</div>}
      {error && <div className="error">{error}</div>}
      {children}
      {/* TODO: Implement component UI */}
    </div>
  );
};

export default ${componentName};
`;
  }

  private generateServiceContent(filePath: string, context: AgentContext): string {
    const serviceName = this.extractNameFromPath(filePath) || 'Service';
    
    return `export interface ${serviceName}Config {
  // TODO: Define service configuration
}

export class ${serviceName} {
  private config: ${serviceName}Config;
  private initialized = false;

  constructor(config: ${serviceName}Config) {
    this.config = config;
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;
    
    try {
      // TODO: Implement service initialization
      this.initialized = true;
    } catch (error) {
      throw new Error(\`Failed to initialize ${serviceName}: \${error}\`);
    }
  }

  async execute(data: any): Promise<any> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      // TODO: Implement main service logic
      return { success: true, data };
    } catch (error) {
      throw new Error(\`${serviceName} execution failed: \${error}\`);
    }
  }

  isInitialized(): boolean {
    return this.initialized;
  }
}

export const ${serviceName.toLowerCase()} = new ${serviceName}({
  // TODO: Add default configuration
});
`;
  }

  private generateControllerContent(filePath: string, context: AgentContext): string {
    const controllerName = this.extractNameFromPath(filePath) || 'Controller';
    
    return `export class ${controllerName} {
  async get(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // TODO: Implement GET logic
      const result = { message: 'Success', data: null };
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  async post(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { body } = req;
      // TODO: Implement POST logic
      const result = { message: 'Created', data: body };
      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  async put(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { params, body } = req;
      // TODO: Implement PUT logic
      const result = { message: 'Updated', data: body };
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  async delete(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { params } = req;
      // TODO: Implement DELETE logic
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  }
}

export const ${controllerName.toLowerCase()} = new ${controllerName}();
`;
  }

  private generateModelContent(filePath: string, context: AgentContext): string {
    const modelName = this.extractNameFromPath(filePath) || 'Model';
    
    return `export interface ${modelName}Data {
  id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  // TODO: Add specific model fields
}

export class ${modelName} {
  private data: ${modelName}Data;

  constructor(data: Partial<${modelName}Data> = {}) {
    this.data = {
      id: data.id || this.generateId(),
      createdAt: data.createdAt || new Date(),
      updatedAt: data.updatedAt || new Date(),
      ...data
    };
  }

  static async findById(id: string): Promise<${modelName} | null> {
    try {
      // TODO: Implement database query
      return null;
    } catch (error) {
      throw new Error(\`Failed to find ${modelName} by id: \${error}\`);
    }
  }

  static async findAll(filters: Partial<${modelName}Data> = {}): Promise<${modelName}[]> {
    try {
      // TODO: Implement database query with filters
      return [];
    } catch (error) {
      throw new Error(\`Failed to find ${modelName}s: \${error}\`);
    }
  }

  async save(): Promise<${modelName}> {
    try {
      this.data.updatedAt = new Date();
      // TODO: Implement database save operation
      return this;
    } catch (error) {
      throw new Error(\`Failed to save ${modelName}: \${error}\`);
    }
  }

  async delete(): Promise<void> {
    try {
      // TODO: Implement database delete operation
    } catch (error) {
      throw new Error(\`Failed to delete ${modelName}: \${error}\`);
    }
  }

  toJSON(): ${modelName}Data {
    return { ...this.data };
  }

  private generateId(): string {
    return \`\${modelName.toLowerCase()}_\${Date.now()}_\${Math.random().toString(36).substr(2, 9)}\`;
  }
}
`;
  }

  private generateTypesContent(filePath: string, context: AgentContext): string {
    return `// Type definitions and interfaces
// Generated for multi-file implementation

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// TODO: Add specific types based on implementation requirements

export type RequestStatus = 'pending' | 'processing' | 'completed' | 'failed';

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ConfigOptions {
  debug?: boolean;
  timeout?: number;
  retries?: number;
}
`;
  }

  private generateTestContent(filePath: string, context: AgentContext): string {
    const testSubject = this.extractNameFromPath(filePath.replace('.test', '').replace('.spec', '')) || 'Component';
    
    return `// Integration tests for multi-file implementation
// Generated by MidLevel Agent

describe('${testSubject} Integration', () => {
  beforeEach(() => {
    // Setup test environment
  });

  afterEach(() => {
    // Cleanup test environment
  });

  describe('Component Integration', () => {
    test('components work together correctly', async () => {
      // TODO: Test component integration
    });

    test('handles data flow between components', async () => {
      // TODO: Test data flow
    });
  });

  describe('Service Integration', () => {
    test('services communicate correctly', async () => {
      // TODO: Test service communication
    });

    test('handles error propagation', async () => {
      // TODO: Test error handling across services
    });
  });

  describe('API Integration', () => {
    test('API endpoints work with services', async () => {
      // TODO: Test API integration
    });

    test('handles request/response flow', async () => {
      // TODO: Test request/response flow
    });
  });

  describe('Database Integration', () => {
    test('model operations work correctly', async () => {
      // TODO: Test database operations
    });

    test('handles transactions', async () => {
      // TODO: Test transaction handling
    });
  });
});
`;
  }

  private generateGenericContent(filePath: string, context: AgentContext): string {
    return `// Generic implementation for ${filePath}
// Part of multi-file implementation: ${context.task}

// TODO: Implement specific functionality for this file
export default {};
`;
  }

  private validateMultiFileImplementation(modifications: FileModification[], analysis: any): { valid: boolean; error?: string } {
    if (modifications.length === 0) {
      return { valid: false, error: 'No file modifications generated' };
    }

    // Check for circular dependencies
    const dependencyCheck = this.checkCircularDependencies(modifications);
    if (!dependencyCheck.valid) {
      return dependencyCheck;
    }

    // Check for integration consistency
    const integrationCheck = this.checkIntegrationConsistency(modifications, analysis);
    if (!integrationCheck.valid) {
      return integrationCheck;
    }

    // Check for too many TODOs
    const todoCount = modifications.reduce((count, mod) => {
      if (mod.content) {
        return count + (mod.content.match(/TODO:/g) || []).length;
      }
      return count;
    }, 0);

    if (todoCount > modifications.length * 3) {
      return { valid: false, error: 'Too many TODO items across files - implementation appears incomplete' };
    }

    return { valid: true };
  }

  private checkCircularDependencies(modifications: FileModification[]): { valid: boolean; error?: string } {
    // Simple circular dependency check
    // In a real implementation, this would analyze import statements
    return { valid: true };
  }

  private checkIntegrationConsistency(modifications: FileModification[], analysis: any): { valid: boolean; error?: string } {
    // Check that integration points are addressed
    const hasIntegrationCode = modifications.some(mod => 
      mod.content && (
        mod.content.includes('integration') || 
        mod.content.includes('interface') ||
        mod.content.includes('API')
      )
    );

    if (analysis.integrationPoints.length > 0 && !hasIntegrationCode) {
      return { valid: false, error: 'Integration points identified but no integration code generated' };
    }

    return { valid: true };
  }

  private generateIntegrationTests(modifications: FileModification[], analysis: any): string {
    return `// Integration Tests
// Generated by MidLevel Agent for multi-file implementation

describe('Multi-File Integration Tests', () => {
  describe('File Interactions', () => {
    test('all files work together correctly', async () => {
      // TODO: Test integration between all modified files
      expect(true).toBe(true); // Placeholder
    });
  });

  describe('Data Flow', () => {
    test('data flows correctly between components', async () => {
      // TODO: Test data flow across files
    });
  });

  describe('Error Handling', () => {
    test('errors are handled consistently across files', async () => {
      // TODO: Test error handling integration
    });
  });
});
`;
  }

  private formatMultiFileResponse(modifications: FileModification[], tests: string, analysis: any): string {
    let response = `[MIDLEVEL MULTI-FILE IMPLEMENTATION]

TASK: ${analysis.type}
COMPLEXITY: ${analysis.complexity}
FILES MODIFIED: ${modifications.length}

IMPLEMENTATION SUMMARY:
${modifications.map(mod => `- ${mod.action.toUpperCase()}: ${mod.path}`).join('\n')}

INTEGRATION POINTS:
${analysis.integrationPoints.join(', ')}

PATTERNS APPLIED:
${analysis.patterns.join(', ')}

`;

    // Add file contents
    modifications.forEach(mod => {
      if (mod.content) {
        response += `\n--- FILE: ${mod.path} ---\n`;
        response += mod.content;
        response += `\n--- END FILE: ${mod.path} ---\n`;
      }
    });

    // Add integration tests
    response += `\n--- INTEGRATION TESTS ---\n`;
    response += tests;
    response += `\n--- END INTEGRATION TESTS ---\n`;

    return response;
  }

  private generateSuggestions(analysis: any, hadRetries: boolean): string[] {
    const suggestions: string[] = [];

    if (hadRetries) {
      suggestions.push('Multi-file implementation required multiple attempts - consider reviewing file dependencies');
    }

    if (analysis.affectedFiles.length > 5) {
      suggestions.push('Large number of files affected - consider breaking into smaller features');
    }

    if (analysis.integrationPoints.length > 3) {
      suggestions.push('Complex integration detected - add comprehensive integration tests');
    }

    if (analysis.complexity === 'complex') {
      suggestions.push('Complex multi-file task - consider code review and architectural validation');
    }

    suggestions.push('Add comprehensive documentation for file relationships');
    suggestions.push('Consider implementing monitoring for cross-file interactions');
    suggestions.push('Add logging for debugging multi-component issues');

    if (analysis.patterns.length > 0) {
      suggestions.push(`Applied patterns: ${analysis.patterns.join(', ')} - ensure consistent implementation`);
    }

    return suggestions;
  }

  private extractNameFromPath(filePath: string): string | null {
    const parts = filePath.split('/');
    const fileName = parts[parts.length - 1];
    const nameWithoutExt = fileName.split('.')[0];
    
    // Convert to PascalCase
    return nameWithoutExt
      .split(/[-_]/)
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join('');
  }
}