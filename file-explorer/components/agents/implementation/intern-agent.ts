// components/agents/implementation/intern-agent.ts
import { <PERSON>Base, AgentConfig, AgentContext, AgentResponse } from '../agent-base';
import { LLMRequestService, LLMMessage } from '../llm-request-service';

export class InternAgent extends AgentBase {
  private llmService: LLMRequestService;

  constructor(config: AgentConfig) {
    super(config);
    this.llmService = LLMRequestService.getInstance();
  }

  public getCapabilities(): string[] {
    return [
      'simple_tasks',
      'boilerplate_generation',
      'template_implementation',
      'basic_file_operations',
      'simple_debugging'
    ];
  }

  public getSystemPrompt(): string {
    return `You are the Intern implementation agent, responsible for handling simple, well-defined coding tasks.

CORE RESPONSIBILITIES:
1. Implement straightforward, clearly specified code following explicit instructions
2. Focus only on the context provided for your specific task
3. Try one straightforward solution, report issues if unsuccessful
4. Stick strictly to simple, well-defined tasks

CAPABILITIES:
- Single file, template-based tasks
- Boilerplate code generation
- Simple function implementations
- Basic file operations
- Following explicit patterns

ESCALATION CRITERIA:
- Complex algorithms or logic
- Multiple file interactions
- Architectural decisions
- Performance optimization

Focus on accuracy and consistency rather than innovation.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      const validation = this.validateContext(context);
      if (!validation.valid) {
        return this.createErrorResponse(validation.error!);
      }

      // ✅ Task 86: Explicit guard for LLM configuration
      if (!this.config.provider || !this.config.model) {
        throw new Error(`❌ Task 86: LLM config missing for InternAgent. Provider: ${this.config.provider}, Model: ${this.config.model}`);
      }

      // Check if task is suitable for intern level
      if (!this.isTaskSuitableForIntern(context.task)) {
        return this.createErrorResponse(
          'Task complexity exceeds Intern capabilities. Consider Junior or higher level agent.'
        );
      }

      // ✅ Task 80: Generate context hash and check cache first
      const contextHash = this.generateContextHash(context);
      const taskId = context.metadata?.originalTaskId || `intern-${Date.now()}`;

      // Check if we have a cached response
      const cachedResponse = await this.getCachedLLMResponse(taskId);
      if (cachedResponse) {
        console.log(`🔍 InternAgent: Using cached response for task ${taskId}`);
        const executionTime = Date.now() - startTime;
        return this.createSuccessResponse(
          cachedResponse,
          0, // No tokens used for cached response
          executionTime,
          ['Response retrieved from cache', 'No LLM call required'],
          {
            cached: true,
            contextHash,
            taskType: 'simple_implementation'
          }
        );
      }

      // ✅ Task 82: Get shared context from previous agents in the chain
      const chainContext = await this.getChainContext(taskId, ['plan_outline', 'architecture_decisions']);

      let contextualInfo = '';
      if (Object.keys(chainContext).length > 0) {
        contextualInfo = '\n\nShared Context from Previous Agents:\n';
        for (const [key, value] of Object.entries(chainContext)) {
          if (typeof value === 'object' && value !== null) {
            contextualInfo += `${key}:\n${JSON.stringify(value, null, 2)}\n\n`;
          } else {
            contextualInfo += `${key}: ${value}\n\n`;
          }
        }
        console.log(`🔍 InternAgent: Using shared context from ${Object.keys(chainContext).length} previous agents`);
      }

      // Prepare messages for LLM
      const messages: LLMMessage[] = [
        {
          role: 'system',
          content: this.getSystemPrompt()
        },
        {
          role: 'user',
          content: this.buildUserPrompt(context) + contextualInfo
        }
      ];

      // Call LLM service
      const llmResponse = await this.llmService.callLLM(this.config, messages);

      // ✅ Task 86: Add completion logging to executionLogStore
      const { executionLogger } = await import('../agent-execution-trace');
      executionLogger.record({
        agentId: this.getId(),
        taskId: context.metadata?.taskId || context.metadata?.originalTaskId,
        cardId: context.metadata?.kanbanCardId,
        output: llmResponse.content,
        modelUsed: this.config.model,
        tokensUsed: llmResponse.tokensUsed.total,
        provider: this.config.provider,
        executionTime: Date.now() - startTime,
        success: true
      });

      // ✅ Task 80: Cache the LLM response for future use
      if (llmResponse.content) {
        await this.cacheLLMResponse(
          taskId,
          llmResponse.content,
          llmResponse.tokensUsed.total,
          llmResponse.model,
          llmResponse.provider,
          llmResponse.finishReason,
          llmResponse.responseTime,
          contextHash,
          {
            taskType: 'simple_implementation',
            timestamp: Date.now()
          }
        );
        console.log(`🔍 InternAgent: Cached response for task ${taskId}`);
      }

      // ✅ Task 82: Store implementation results for other agents
      if (llmResponse.content) {
        await this.storeSharedContext(
          taskId,
          'component_map',
          {
            implementation: llmResponse.content,
            implementationType: 'simple_implementation',
            codeGenerated: 'extracted_from_response',
            filesModified: context.files || [],
            implementationNotes: 'intern_level_implementation'
          },
          {
            tags: ['intern', 'implementation', 'code_generation'],
            parentTaskId: context.metadata?.parentTaskId,
            metadata: {
              provider: llmResponse.provider,
              model: llmResponse.model,
              tokensUsed: llmResponse.tokensUsed.total,
              basedOnContext: Object.keys(chainContext).length > 0,
              contextSources: Object.keys(chainContext)
            }
          }
        );
        console.log(`🔍 InternAgent: Stored implementation results for task ${taskId}`);
      }

      const executionTime = Date.now() - startTime;

      return this.createSuccessResponse(
        llmResponse.content,
        llmResponse.tokensUsed.total,
        executionTime,
        ['Task completed with AI-generated implementation', 'Consider review by higher-level agent'],
        {
          taskType: 'simple_implementation',
          provider: llmResponse.provider,
          model: llmResponse.model,
          finishReason: llmResponse.finishReason,
          contextHash,
          cached: false,
          sharedContextUsed: Object.keys(chainContext).length > 0,
          contextSources: Object.keys(chainContext)
        }
      );

    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Intern task failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context),
        executionTime
      );
    }
  }

  private isTaskSuitableForIntern(task: string): boolean {
    const task_lower = task.toLowerCase();

    // Tasks too complex for intern
    const complexIndicators = [
      'complex', 'algorithm', 'architecture', 'performance', 'optimization',
      'multiple files', 'integration', 'database', 'api design'
    ];

    return !complexIndicators.some(indicator => task_lower.includes(indicator));
  }

  private buildUserPrompt(context: AgentContext): string {
    let prompt = `Task: ${context.task}\n\n`;

    if (context.codeContext) {
      prompt += `Code Context:\n${context.codeContext}\n\n`;
    }

    if (context.files && context.files.length > 0) {
      prompt += `Related Files: ${context.files.join(', ')}\n\n`;
    }

    if (context.rules && context.rules.length > 0) {
      prompt += `Rules to Follow:\n${context.rules.map(rule => `- ${rule}`).join('\n')}\n\n`;
    }

    prompt += `Please provide a simple, clean implementation for this task. Focus on:
- Clear, readable code
- Basic functionality
- Following established patterns
- Proper error handling where needed

Provide only the code implementation without extensive explanations.`;

    return prompt;
  }
}
