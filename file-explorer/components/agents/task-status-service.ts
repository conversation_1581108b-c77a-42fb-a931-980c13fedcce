// components/agents/task-status-service.ts
import { AgentResponse } from './agent-base';
import { TaskCoordinationResult } from './agent-task-coordinator';
import { KanbanTaskBridge } from './kanban-task-bridge';

export interface TaskStatusUpdate {
  taskId: string;
  agentId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'retrying' | 'waiting';
  progress?: number;
  message?: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface TaskStatusCallbacks {
  onStatusUpdate?: (update: TaskStatusUpdate) => void;
  onProgressUpdate?: (taskId: string, progress: number) => void;
  onKanbanUpdate?: (taskId: string, cardId: string, status: string) => void;
}

export interface TaskMetrics {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  runningTasks: number;
  waitingTasks: number;
  retryingTasks: number;
  averageExecutionTime: number;
  successRate: number;
  totalExecutionTime: number;
}

export class TaskStatusService {
  private static instance: TaskStatusService;
  private statusHistory: Map<string, TaskStatusUpdate[]> = new Map();
  private currentStatuses: Map<string, TaskStatusUpdate> = new Map();
  private callbacks: TaskStatusCallbacks = {};
  private taskMetrics: TaskMetrics = {
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    runningTasks: 0,
    waitingTasks: 0,
    retryingTasks: 0,
    averageExecutionTime: 0,
    successRate: 0,
    totalExecutionTime: 0
  };
  private taskStartTimes: Map<string, number> = new Map();

  public static getInstance(): TaskStatusService {
    if (!TaskStatusService.instance) {
      TaskStatusService.instance = new TaskStatusService();
    }
    return TaskStatusService.instance;
  }

  /**
   * Set callbacks for status updates
   */
  public setCallbacks(callbacks: TaskStatusCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Update task status with real-time sync
   */
  public async updateTaskStatus(
    taskId: string,
    agentId: string,
    status: TaskStatusUpdate['status'],
    options: {
      progress?: number;
      message?: string;
      metadata?: Record<string, any>;
      kanbanCardId?: string;
    } = {}
  ): Promise<void> {
    const timestamp = Date.now();

    const statusUpdate: TaskStatusUpdate = {
      taskId,
      agentId,
      status,
      progress: options.progress,
      message: options.message,
      timestamp,
      metadata: options.metadata
    };

    // Store current status
    this.currentStatuses.set(taskId, statusUpdate);

    // Add to history
    if (!this.statusHistory.has(taskId)) {
      this.statusHistory.set(taskId, []);
    }
    this.statusHistory.get(taskId)!.push(statusUpdate);

    // Track task timing
    if (status === 'running' && !this.taskStartTimes.has(taskId)) {
      this.taskStartTimes.set(taskId, timestamp);
    } else if ((status === 'completed' || status === 'failed') && this.taskStartTimes.has(taskId)) {
      const startTime = this.taskStartTimes.get(taskId)!;
      const executionTime = timestamp - startTime;
      this.taskStartTimes.delete(taskId);

      // Update metrics
      this.updateMetrics(status, executionTime);
    }

    // Update Kanban card if provided
    if (options.kanbanCardId) {
      await this.updateKanbanCard(taskId, options.kanbanCardId, status, agentId, options.progress);
    }

    // Notify callbacks
    if (this.callbacks.onStatusUpdate) {
      this.callbacks.onStatusUpdate(statusUpdate);
    }

    if (options.progress !== undefined && this.callbacks.onProgressUpdate) {
      this.callbacks.onProgressUpdate(taskId, options.progress);
    }

    console.log(`TaskStatusService: Updated task ${taskId} status to ${status}${options.progress !== undefined ? ` (${options.progress}%)` : ''}`);
  }

  /**
   * Update Kanban card based on task status
   */
  private async updateKanbanCard(
    taskId: string,
    cardId: string,
    status: TaskStatusUpdate['status'],
    agentId: string,
    progress?: number
  ): Promise<void> {
    try {
      // Update card progress
      if (progress !== undefined) {
        await KanbanTaskBridge.updateCardProgress(cardId, progress, agentId);
      }

      // Move card based on status
      let targetStatus: 'pending' | 'running' | 'completed' | 'failed';
      switch (status) {
        case 'waiting':
        case 'pending':
          targetStatus = 'pending';
          break;
        case 'running':
        case 'retrying':
          targetStatus = 'running';
          break;
        case 'completed':
          targetStatus = 'completed';
          break;
        case 'failed':
          targetStatus = 'failed';
          break;
        default:
          targetStatus = 'pending';
      }

      // ✅ Check if card is already in the target column to prevent duplicate moves
      const currentCardState = await this.getCurrentCardState(cardId);

      // Map status to column IDs
      const columnMap = {
        'pending': 'column-1',    // Backlog
        'running': 'column-3',    // In Development
        'completed': 'column-6',  // Done
        'failed': 'column-1'      // Back to Backlog
      };

      const targetColumnId = columnMap[targetStatus];

      // Only move if card is not already in the target column
      if (currentCardState && currentCardState.columnId === targetColumnId) {
        console.log(`⚠️ [TaskStatusService] Card ${cardId} is already in target column ${targetColumnId} for status ${status}, skipping move`);

        // Still notify callback even if we skip the move
        if (this.callbacks.onKanbanUpdate) {
          this.callbacks.onKanbanUpdate(taskId, cardId, status);
        }
        return;
      }

      console.log(`🔄 [TaskStatusService] Moving card ${cardId} to ${targetStatus} status (column ${targetColumnId}) for task ${taskId}`);
      await KanbanTaskBridge.moveCardBasedOnTaskStatus(cardId, targetStatus, agentId);

      // Notify Kanban update callback
      if (this.callbacks.onKanbanUpdate) {
        this.callbacks.onKanbanUpdate(taskId, cardId, status);
      }

      console.log(`TaskStatusService: Updated Kanban card ${cardId} for task ${taskId} to ${targetStatus}`);
    } catch (error) {
      console.error(`TaskStatusService: Failed to update Kanban card ${cardId}:`, error);
    }
  }

  /**
   * ✅ Get current card state to prevent duplicate moves
   */
  private async getCurrentCardState(cardId: string): Promise<{ columnId: string; title: string } | null> {
    try {
      // Import board IPC bridge dynamically to avoid circular dependencies
      const { boardIPCBridge } = await import('../kanban/lib/board-ipc-bridge');

      // Get board state and find the card
      const boardState = await boardIPCBridge.getBoardState('main');
      if (!boardState) {
        console.warn(`TaskStatusService: Failed to get board state for card ${cardId}`);
        return null;
      }

      // Find the card across all columns
      for (const column of boardState.columns) {
        const card = column.cards.find(c => c.id === cardId);
        if (card) {
          return {
            columnId: column.id,
            title: card.title
          };
        }
      }

      console.warn(`TaskStatusService: Card ${cardId} not found in any column`);
      return null;
    } catch (error) {
      console.warn(`TaskStatusService: Failed to get current state for card ${cardId}:`, error);
      return null;
    }
  }

  /**
   * Update task metrics
   */
  private updateMetrics(status: 'completed' | 'failed', executionTime: number): void {
    this.taskMetrics.totalTasks++;
    this.taskMetrics.totalExecutionTime += executionTime;

    if (status === 'completed') {
      this.taskMetrics.completedTasks++;
    } else if (status === 'failed') {
      this.taskMetrics.failedTasks++;
    }

    // Calculate averages
    this.taskMetrics.averageExecutionTime = this.taskMetrics.totalExecutionTime / this.taskMetrics.totalTasks;
    this.taskMetrics.successRate = (this.taskMetrics.completedTasks / this.taskMetrics.totalTasks) * 100;

    // Update current counts
    this.updateCurrentCounts();
  }

  /**
   * Update current task counts
   */
  private updateCurrentCounts(): void {
    const currentStatuses = Array.from(this.currentStatuses.values());

    this.taskMetrics.runningTasks = currentStatuses.filter(s => s.status === 'running').length;
    this.taskMetrics.waitingTasks = currentStatuses.filter(s => s.status === 'waiting' || s.status === 'pending').length;
    this.taskMetrics.retryingTasks = currentStatuses.filter(s => s.status === 'retrying').length;
  }

  /**
   * Get current task status
   */
  public getTaskStatus(taskId: string): TaskStatusUpdate | null {
    return this.currentStatuses.get(taskId) || null;
  }

  /**
   * Get task status history
   */
  public getTaskHistory(taskId: string): TaskStatusUpdate[] {
    return this.statusHistory.get(taskId) || [];
  }

  /**
   * Get all current task statuses
   */
  public getAllCurrentStatuses(): TaskStatusUpdate[] {
    return Array.from(this.currentStatuses.values());
  }

  /**
   * Get task metrics
   */
  public getMetrics(): TaskMetrics {
    this.updateCurrentCounts();
    return { ...this.taskMetrics };
  }

  /**
   * Get tasks by status
   */
  public getTasksByStatus(status: TaskStatusUpdate['status']): TaskStatusUpdate[] {
    return Array.from(this.currentStatuses.values()).filter(s => s.status === status);
  }

  /**
   * Get tasks by agent
   */
  public getTasksByAgent(agentId: string): TaskStatusUpdate[] {
    return Array.from(this.currentStatuses.values()).filter(s => s.agentId === agentId);
  }

  /**
   * Report task progress
   */
  public async reportProgress(
    taskId: string,
    agentId: string,
    progress: number,
    message?: string,
    kanbanCardId?: string
  ): Promise<void> {
    await this.updateTaskStatus(taskId, agentId, 'running', {
      progress,
      message,
      kanbanCardId
    });
  }

  /**
   * Report task completion
   */
  public async reportCompletion(
    taskId: string,
    agentId: string,
    result: AgentResponse,
    kanbanCardId?: string
  ): Promise<void> {
    await this.updateTaskStatus(taskId, agentId, 'completed', {
      progress: 100,
      message: result.output,
      metadata: {
        result,
        tokensUsed: result.tokensUsed,
        executionTime: result.executionTime
      },
      kanbanCardId
    });
  }

  /**
   * Report task error
   */
  public async reportError(
    taskId: string,
    agentId: string,
    error: string,
    willRetry: boolean = false,
    kanbanCardId?: string
  ): Promise<void> {
    const status = willRetry ? 'retrying' : 'failed';

    await this.updateTaskStatus(taskId, agentId, status, {
      message: error,
      metadata: {
        error,
        willRetry
      },
      kanbanCardId
    });
  }

  /**
   * Report task waiting for dependencies
   */
  public async reportWaiting(
    taskId: string,
    agentId: string,
    dependencies: string[],
    kanbanCardId?: string
  ): Promise<void> {
    await this.updateTaskStatus(taskId, agentId, 'waiting', {
      message: `Waiting for dependencies: ${dependencies.join(', ')}`,
      metadata: {
        dependencies
      },
      kanbanCardId
    });
  }

  /**
   * Get status summary for dashboard
   */
  public getStatusSummary(): {
    metrics: TaskMetrics;
    recentUpdates: TaskStatusUpdate[];
    activeAgents: string[];
    criticalTasks: TaskStatusUpdate[];
  } {
    const metrics = this.getMetrics();
    const allStatuses = this.getAllCurrentStatuses();

    // Get recent updates (last 10)
    const allUpdates = Array.from(this.statusHistory.values()).flat();
    const recentUpdates = allUpdates
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 10);

    // Get active agents
    const activeAgents = [...new Set(allStatuses.map(s => s.agentId))];

    // Get critical tasks (failed or retrying)
    const criticalTasks = allStatuses.filter(s => s.status === 'failed' || s.status === 'retrying');

    return {
      metrics,
      recentUpdates,
      activeAgents,
      criticalTasks
    };
  }

  /**
   * Clear status data
   */
  public clearStatus(): void {
    this.statusHistory.clear();
    this.currentStatuses.clear();
    this.taskStartTimes.clear();
    this.taskMetrics = {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      runningTasks: 0,
      waitingTasks: 0,
      retryingTasks: 0,
      averageExecutionTime: 0,
      successRate: 0,
      totalExecutionTime: 0
    };
  }

  /**
   * Export status data for analysis
   */
  public exportStatusData(): {
    currentStatuses: Record<string, TaskStatusUpdate>;
    statusHistory: Record<string, TaskStatusUpdate[]>;
    metrics: TaskMetrics;
  } {
    return {
      currentStatuses: Object.fromEntries(this.currentStatuses),
      statusHistory: Object.fromEntries(this.statusHistory),
      metrics: this.getMetrics()
    };
  }
}
