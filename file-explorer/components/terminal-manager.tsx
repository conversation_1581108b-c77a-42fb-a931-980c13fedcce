"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, X, RefreshCw, Trash2, Command } from "lucide-react"
import { cn } from "@/lib/utils"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useTheme } from "next-themes"

type Terminal = {
  id: number
  name: string
  content: string[]
  cwd: string
  shellType: "bash" | "powershell" | "cmd" | "zsh"
}

// Function to apply syntax highlighting to terminal output
const highlightTerminalOutput = (text: string, isDark: boolean): string => {
  // Replace < and > with their HTML entities to prevent rendering as HTML
  let processed = text.replace(/</g, "&lt;").replace(/>/g, "&gt;")

  // Highlight common terminal patterns

  // Command prompts ($ or >)
  processed = processed.replace(
    /^(\$|>)(.*)$/gm,
    '<span class="terminal-prompt">$1</span><span class="terminal-command">$2</span>',
  )

  // URLs
  processed = processed.replace(/(https?:\/\/[^\s]+)/g, '<span class="terminal-url">$1</span>')

  // File paths
  processed = processed.replace(/(~\/[\w/.-]+|\/[\w/.-]+|[A-Z]:\\[\w\\.-]+)/g, '<span class="terminal-path">$1</span>')

  // Success messages
  processed = processed.replace(
    /(success|succeeded|completed|done|ok|running|connected|initialized)/gi,
    '<span class="terminal-success">$1</span>',
  )

  // Error messages
  processed = processed.replace(
    /(error|failed|failure|warning|not found|cannot|invalid)/gi,
    '<span class="terminal-error">$1</span>',
  )

  // Numbers
  processed = processed.replace(/\b(\d+(\.\d+)?)\b/g, '<span class="terminal-number">$1</span>')

  // Package names (npm style)
  processed = processed.replace(/(@[\w-]+\/[\w-]+|[\w-]+@[\d.]+)/g, '<span class="terminal-package">$1</span>')

  // Commands
  processed = processed.replace(
    /\b(npm|node|git|docker|yarn|ls|cd|mkdir|rm|cp|mv)\b/g,
    '<span class="terminal-command-name">$1</span>',
  )

  // Options and flags
  processed = processed.replace(/\s(-{1,2}[a-zA-Z0-9-]+)/g, ' <span class="terminal-option">$1</span>')

  return processed
}

export default function TerminalManager({ isRunning }: { isRunning: boolean }) {
  const [terminals, setTerminals] = useState<Terminal[]>([
    {
      id: 1,
      name: "bash",
      content: ["Welcome to the terminal. Type 'help' for available commands."],
      cwd: "~/projects",
      shellType: "bash",
    },
  ])
  const [activeTerminalId, setActiveTerminalId] = useState<number>(1)
  const [terminalInput, setTerminalInput] = useState<string>("")
  const inputRef = useRef<HTMLInputElement>(null)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const { theme } = useTheme()
  const isDark = theme === "dark"

  // Focus input when terminal is clicked
  const focusInput = () => {
    inputRef.current?.focus()
  }

  // Add a new terminal
  const addTerminal = (shellType: "bash" | "powershell" | "cmd" | "zsh" = "bash") => {
    const newId = terminals.length > 0 ? Math.max(...terminals.map((t) => t.id)) + 1 : 1
    const newTerminal: Terminal = {
      id: newId,
      name: `${shellType}-${newId}`,
      content: [`Welcome to ${shellType}. Type 'help' for available commands.`],
      cwd: "~/projects",
      shellType,
    }
    setTerminals([...terminals, newTerminal])
    setActiveTerminalId(newId)
  }

  // Close a terminal
  const closeTerminal = (id: number, e?: React.MouseEvent) => {
    e?.stopPropagation()

    if (terminals.length === 1) {
      // If this is the last terminal, add a new one before removing
      addTerminal()
      setTerminals((prev) => prev.filter((t) => t.id !== id))
    } else {
      setTerminals((prev) => prev.filter((t) => t.id !== id))
      // If the active terminal is being closed, activate another one
      if (activeTerminalId === id) {
        const remainingTerminals = terminals.filter((t) => t.id !== id)
        setActiveTerminalId(remainingTerminals[0].id)
      }
    }
  }

  // Clear terminal content
  const clearTerminal = () => {
    setTerminals((prev) => prev.map((t) => (t.id === activeTerminalId ? { ...t, content: [] } : t)))
  }

  // Handle terminal input
  const handleTerminalInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && terminalInput.trim()) {
      const command = terminalInput.trim()

      // Process command
      let response: string[] = []

      if (command === "clear") {
        clearTerminal()
        setTerminalInput("")
        return
      } else if (command === "ls" || command === "dir") {
        response = ["No files in current directory"]
      } else if (command.startsWith("cd ")) {
        const dir = command.substring(3)
        setTerminals((prev) => prev.map((t) => (t.id === activeTerminalId ? { ...t, cwd: `~/projects/${dir}` } : t)))
      } else if (command === "help") {
        response = [
          "Available commands:",
          "clear - Clear the terminal",
          "ls/dir - List files and directories",
          "cd [dir] - Change directory",
          "help - Show this help message",
        ]
      } else {
        response = [`Command not found: ${command}. Type 'help' for available commands.`]
      }

      // Update terminal content
      setTerminals((prev) =>
        prev.map((t) =>
          t.id === activeTerminalId
            ? {
                ...t,
                content: [
                  ...t.content,
                  `${t.shellType === "bash" || t.shellType === "zsh" ? "$" : ">"} ${command}`,
                  ...response,
                ],
              }
            : t,
        ),
      )

      setTerminalInput("")
    }
  }

  // Scroll to bottom when content changes
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector("[data-radix-scroll-area-viewport]")
      if (scrollElement) {
        scrollElement.scrollTop = scrollElement.scrollHeight
      }
    }
  }, [terminals])

  // Get the active terminal
  const activeTerminal = terminals.find((t) => t.id === activeTerminalId) || terminals[0]

  return (
    <div className="flex flex-col h-full" onClick={focusInput}>
      {/* Terminal tabs */}
      <div className="flex items-center border-b border-editor-border bg-editor-sidebar-bg overflow-x-auto hide-scrollbar">
        {terminals.map((terminal) => (
          <div
            key={terminal.id}
            className={cn(
              "flex items-center h-8 px-3 border-r border-editor-border cursor-pointer group",
              terminal.id === activeTerminalId
                ? "bg-editor-terminal-bg text-foreground"
                : "bg-editor-sidebar-bg text-muted-foreground hover:bg-editor-terminal-bg/50",
            )}
            onClick={(e) => {
              e.stopPropagation()
              setActiveTerminalId(terminal.id)
            }}
          >
            <span className="text-xs">{terminal.name}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-5 w-5 ml-2 opacity-0 group-hover:opacity-100"
              onClick={(e) => closeTerminal(terminal.id, e)}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        ))}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8 text-muted-foreground hover:text-foreground">
              <Plus className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuItem onClick={() => addTerminal("bash")}>
              <Command className="mr-2 h-4 w-4" />
              <span>Bash</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => addTerminal("powershell")}>
              <Command className="mr-2 h-4 w-4" />
              <span>PowerShell</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => addTerminal("cmd")}>
              <Command className="mr-2 h-4 w-4" />
              <span>Command Prompt</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => addTerminal("zsh")}>
              <Command className="mr-2 h-4 w-4" />
              <span>Zsh</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="ml-auto flex items-center">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-muted-foreground hover:text-foreground"
            onClick={clearTerminal}
            title="Clear Terminal"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Terminal content */}
      <ScrollArea className="flex-1" ref={scrollAreaRef}>
        <div className="p-2 font-mono text-sm">
          {activeTerminal?.content.map((line, index) => (
            <p key={index} dangerouslySetInnerHTML={{ __html: highlightTerminalOutput(line, isDark) }} />
          ))}

          {isRunning && (
            <>
              <p className="text-green-500">Running application...</p>
              <div className="flex items-center mt-1">
                <RefreshCw className="h-3 w-3 mr-2 animate-spin text-editor-highlight" />
                <span>Processing...</span>
              </div>
            </>
          )}

          {/* Terminal input line */}
          <div className="flex items-center mt-1">
            <span className="mr-2 terminal-prompt">
              {activeTerminal?.shellType === "bash" || activeTerminal?.shellType === "zsh" ? "$" : ">"}
            </span>
            <input
              ref={inputRef}
              type="text"
              value={terminalInput}
              onChange={(e) => setTerminalInput(e.target.value)}
              onKeyDown={handleTerminalInput}
              className="flex-1 bg-transparent border-none outline-none focus:ring-0 p-0 text-sm font-mono terminal-command"
              autoFocus
            />
          </div>
        </div>
      </ScrollArea>

      {/* Terminal status bar */}
      <div className="h-6 border-t border-editor-border bg-editor-statusbar-bg flex items-center px-2 text-xs text-muted-foreground">
        <span>{activeTerminal?.shellType}</span>
        <span className="mx-2">•</span>
        <span>{activeTerminal?.cwd}</span>
      </div>
    </div>
  )
}
